@echo off
REM RAG Citation API Installation Script for Windows
REM This script handles Python 3.12.8 compatibility issues

echo 🚀 Starting RAG Citation API installation...

REM Check Python version
python --version
if %ERRORLEVEL% neq 0 (
    echo ❌ Python not found. Please install Python 3.11+ or 3.12+
    pause
    exit /b 1
)

REM Step 1: Upgrade core tools
echo 🔧 Step 1: Upgrading core Python tools...
python -m pip install --upgrade pip
pip install --upgrade setuptools wheel

REM Step 2: Install core dependencies
echo 📦 Step 2: Installing core dependencies...
pip install -r requirements-core.txt

REM Step 3: Install ML dependencies
echo 🤖 Step 3: Installing ML/AI dependencies...
pip install -r requirements-ml.txt

REM Step 4: Install development dependencies (optional)
echo 🛠️ Step 4: Installing development dependencies...
pip install "pytest>=7.4.3,<8.0.0"
pip install "pytest-asyncio>=0.21.1,<1.0.0"
pip install "black>=23.12.1,<25.0.0"
pip install "isort>=5.13.2,<6.0.0"

REM Step 5: Verify installation
echo ✅ Step 5: Verifying installation...
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')"
python -c "import anthropic; print(f'Anthropic: {anthropic.__version__}')"
python -c "import openai; print(f'OpenAI: {openai.__version__}')"

echo 🎉 Installation completed successfully!
echo.
echo Next steps:
echo 1. Copy .env.example to .env and configure your API keys
echo 2. Run: python run.py
echo 3. Visit: http://localhost:8000/docs

pause
