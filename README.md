# RAG Citation API

A production-ready REST API server for Retrieval-Augmented Generation (RAG) with integrated citation support using Anthropic's Citation API. This system processes academic documents, stores them in a vector database, and provides question-answering capabilities with proper source citations.

## Features

- **Document Upload & Processing**: Support for PDF, TXT, and Markdown files
- **Vector Database**: KDB.AI integration for efficient similarity search
- **Citation Support**: Anthropic's Citation API for accurate source attribution
- **RESTful API**: FastAPI-based server with automatic documentation
- **Production Ready**: Comprehensive error handling, logging, and validation

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client App    │    │   FastAPI       │    │   Document      │
│                 │───▶│   Server        │───▶│   Processing    │
│                 │    │                 │    │   (LangChain)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   Anthropic     │    │   Vector DB     │
                       │   Citation API  │    │   (KDB.AI)      │
                       └─────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   OpenAI        │    │   Local File    │
                       │   Embeddings    │    │   Storage       │
                       └─────────────────┘    └─────────────────┘
```

## Quick Start

### Prerequisites

- Python 3.8+
- KDB.AI account and API credentials
- Anthropic API key
- OpenAI API key

### Installation

#### Quick Start (Recommended)

1. Clone the repository:
```bash
git clone <repository-url>
cd citation-api
```

2. Run the automated installation script:
```bash
# On macOS/Linux
./install.sh

# On Windows
install.bat
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your API keys and configuration
# IMPORTANT: Remove any inline comments from values
```

4. Validate configuration:
```bash
python validate_config.py
```

5. Run the server:
```bash
python run.py
```

#### Manual Installation (Python 3.12.8 Compatible)

If you encounter setuptools/pkg_resources compatibility issues:

1. Create a clean virtual environment:
```bash
python -m venv venv_clean
source venv_clean/bin/activate  # macOS/Linux
# or venv_clean\Scripts\activate  # Windows
```

2. Upgrade core tools:
```bash
python -m pip install --upgrade pip
pip install --upgrade setuptools>=69.0.0 wheel>=0.42.0
```

3. Install dependencies in stages:
```bash
# Core dependencies first
pip install -r requirements-core.txt

# Then ML/AI dependencies
pip install -r requirements-ml.txt
```

4. Verify installation:
```bash
python -c "from app.main import app; print('✅ Installation successful')"
```

**Note:** For detailed troubleshooting, see [INSTALLATION_GUIDE.md](INSTALLATION_GUIDE.md)

The API will be available at `http://localhost:8000` with interactive documentation at `http://localhost:8000/docs`.

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# Required API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key_here
KDBAI_ENDPOINT=your_kdbai_endpoint_here
KDBAI_API_KEY=your_kdbai_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# Optional Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=False
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50000000
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
```

## API Endpoints

### Health Check
```http
GET /api/v1/health
```

### Document Management

#### Upload Document
```http
POST /api/v1/documents/upload
Content-Type: multipart/form-data

file: <PDF/TXT/MD file>
title: <optional custom title>
```

#### List Documents
```http
GET /api/v1/documents
```

#### Get Document
```http
GET /api/v1/documents/{document_id}
```

#### Delete Document
```http
DELETE /api/v1/documents/{document_id}
```

### Question Answering

#### Query Documents
```http
POST /api/v1/query
Content-Type: application/json

{
  "question": "What are the main findings?",
  "document_ids": ["optional-filter-by-document-ids"],
  "max_results": 5,
  "include_citations": true
}
```

## Usage Examples

### Upload a Document

```python
import requests

# Upload a PDF document
with open("research_paper.pdf", "rb") as f:
    response = requests.post(
        "http://localhost:8000/api/v1/documents/upload",
        files={"file": f},
        data={"title": "Research Paper on AI"}
    )

document = response.json()["document"]
print(f"Uploaded document ID: {document['id']}")
```

### Query with Citations

```python
import requests

# Ask a question about uploaded documents
response = requests.post(
    "http://localhost:8000/api/v1/query",
    json={
        "question": "What are the main contributions of this research?",
        "include_citations": True,
        "max_results": 5
    }
)

result = response.json()
print(f"Answer: {result['answer']}")

# Print citations
for block in result['content_blocks']:
    if block.get('citations'):
        for citation in block['citations']:
            print(f"Citation: {citation['cited_text']} (from {citation['document_title']})")
```

## Development

### Project Structure

```
citation-api/
├── app/
│   ├── __init__.py
│   ├── main.py              # FastAPI application
│   ├── config.py            # Configuration settings
│   ├── models.py            # Pydantic models
│   ├── api/
│   │   ├── __init__.py
│   │   └── endpoints.py     # API endpoints
│   └── services/
│       ├── __init__.py
│       ├── document_service.py    # Document processing
│       ├── vector_service.py      # Vector database operations
│       ├── citation_service.py    # Anthropic Citation API
│       └── embedding_service.py   # OpenAI embeddings
├── uploads/                 # Document storage directory
├── requirements.txt         # Python dependencies
├── .env.example            # Environment variables template
├── run.py                  # Application entry point
└── README.md
```

### Running Tests

```bash
pytest
```

### Code Formatting

```bash
black app/
isort app/
```

## Production Deployment

### Docker Deployment

Create a `Dockerfile`:

```dockerfile
FROM python:3.11-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000

CMD ["python", "run.py"]
```

Build and run:

```bash
docker build -t rag-citation-api .
docker run -p 8000:8000 --env-file .env rag-citation-api
```

### Production Considerations

1. **Security**:
   - Use environment variables for all secrets
   - Configure CORS appropriately
   - Add authentication/authorization
   - Use HTTPS in production

2. **Scalability**:
   - Consider using a proper database instead of in-memory storage
   - Implement caching for embeddings
   - Use a message queue for document processing
   - Add rate limiting

3. **Monitoring**:
   - Add structured logging
   - Implement health checks
   - Monitor API performance
   - Set up error tracking

## Troubleshooting

### Common Issues

1. **KDB.AI Connection Failed**
   - Verify your KDB.AI endpoint and API key
   - Check network connectivity
   - Ensure your KDB.AI account has sufficient credits

2. **Document Processing Errors**
   - Check file format is supported (PDF, TXT, MD)
   - Verify file size is under the limit
   - Ensure sufficient disk space for uploads

3. **Citation Generation Issues**
   - Verify Anthropic API key is valid
   - Check if the model supports citations
   - Monitor API rate limits

### Logs

The application logs to stdout with structured formatting. Key log events include:
- Document upload and processing
- Vector database operations
- Citation generation
- API request/response cycles

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the API documentation at `/docs`
3. Open an issue on GitHub
