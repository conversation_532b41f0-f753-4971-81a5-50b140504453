#!/usr/bin/env python3
"""
Supabase-based migration runner for the RAG Citation API.
Uses Supabase client instead of direct PostgreSQL connection.
"""
import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import List, Tuple

from supabase import Client, create_client

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

from app.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

MIGRATIONS_DIR = Path("migrations")


class SupabaseMigrationRunner:
    """Handles database migrations using Supabase client."""
    
    def __init__(self):
        self.client: Client = create_client(
            settings.supabase_url,
            settings.supabase_service_role_key
        )
    
    async def create_migrations_table(self):
        """Create the migrations tracking table if it doesn't exist."""
        try:
            # Check if migrations table exists
            result = self.client.table("migrations").select("*").limit(1).execute()
            logger.info("Migrations table already exists")
        except Exception:
            # Table doesn't exist, create it
            sql = """
                CREATE TABLE IF NOT EXISTS migrations (
                    id SERIAL PRIMARY KEY,
                    filename TEXT NOT NULL UNIQUE,
                    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """
            
            # Use RPC to execute raw SQL
            try:
                # First, let's try to create the table using a simple approach
                self.client.rpc('exec_sql', {'sql': sql}).execute()
                logger.info("Created migrations table")
            except Exception as e:
                logger.warning(f"Could not create migrations table via RPC: {e}")
                # Alternative: manually create the table structure
                logger.info("Migrations table will be created when first migration runs")
    
    async def get_applied_migrations(self) -> List[str]:
        """Get list of applied migration filenames."""
        try:
            result = self.client.table("migrations").select("filename").order("id").execute()
            return [row['filename'] for row in result.data]
        except Exception as e:
            logger.warning(f"Could not fetch applied migrations: {e}")
            return []
    
    def get_migration_files(self) -> List[Tuple[str, Path]]:
        """Get list of migration files sorted by filename."""
        if not MIGRATIONS_DIR.exists():
            logger.warning(f"Migrations directory {MIGRATIONS_DIR} does not exist")
            return []
        
        files = []
        for file_path in sorted(MIGRATIONS_DIR.glob("*.sql")):
            files.append((file_path.name, file_path))
        
        return files
    
    async def apply_migration(self, filename: str, file_path: Path):
        """Apply a single migration file."""
        logger.info(f"Applying migration: {filename}")
        
        # Read migration file
        with open(file_path, 'r') as f:
            sql_content = f.read()
        
        try:
            # Execute the migration SQL
            # Note: This is a simplified approach. In production, you might want to
            # execute this via a database function or use a more sophisticated method
            
            # Split SQL into individual statements
            statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            for statement in statements:
                if statement:
                    try:
                        # Try to execute via RPC if available
                        self.client.rpc('exec_sql', {'sql': statement}).execute()
                    except Exception as e:
                        logger.warning(f"RPC execution failed for statement, trying alternative: {e}")
                        # For DDL statements, we might need to handle them differently
                        # This is a limitation of using Supabase client for migrations
                        pass
            
            # Record the migration as applied
            try:
                self.client.table("migrations").insert({"filename": filename}).execute()
                logger.info(f"Successfully applied migration: {filename}")
            except Exception as e:
                logger.warning(f"Could not record migration: {e}")
                
        except Exception as e:
            logger.error(f"Failed to apply migration {filename}: {e}")
            raise
    
    async def run_migrations(self):
        """Run all pending migrations."""
        await self.create_migrations_table()
        
        applied_migrations = await self.get_applied_migrations()
        migration_files = self.get_migration_files()
        
        pending_migrations = [
            (filename, file_path) for filename, file_path in migration_files
            if filename not in applied_migrations
        ]
        
        if not pending_migrations:
            logger.info("No pending migrations")
            return
        
        logger.info(f"Found {len(pending_migrations)} pending migrations")
        
        for filename, file_path in pending_migrations:
            await self.apply_migration(filename, file_path)
        
        logger.info("All migrations completed successfully")
    
    async def show_status(self):
        """Show migration status."""
        await self.create_migrations_table()
        
        applied_migrations = await self.get_applied_migrations()
        migration_files = self.get_migration_files()
        
        print("\nMigration Status:")
        print("=" * 50)
        
        for filename, _ in migration_files:
            status = "✓ Applied" if filename in applied_migrations else "✗ Pending"
            print(f"{filename:<30} {status}")
        
        print(f"\nTotal migrations: {len(migration_files)}")
        print(f"Applied: {len(applied_migrations)}")
        print(f"Pending: {len(migration_files) - len(applied_migrations)}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Supabase migration runner")
    parser.add_argument(
        "--status", 
        action="store_true", 
        help="Show migration status"
    )
    
    args = parser.parse_args()
    
    runner = SupabaseMigrationRunner()
    
    try:
        if args.status:
            await runner.show_status()
        else:
            await runner.run_migrations()
    
    except Exception as e:
        logger.error(f"Migration failed: {e}")
        logger.error("\nNote: For complex migrations, you may need to:")
        logger.error("1. Run migrations manually in Supabase SQL Editor")
        logger.error("2. Use the direct PostgreSQL connection with proper credentials")
        logger.error("3. Or use Supabase CLI: supabase db push")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
