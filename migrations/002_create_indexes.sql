-- Create performance indexes for the documents table
-- This migration adds indexes to improve query performance

-- Index for user-specific document queries (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_documents_user_id 
ON documents(user_id);

-- Index for sorting by upload timestamp
CREATE INDEX IF NOT EXISTS idx_documents_upload_timestamp 
ON documents(upload_timestamp DESC);

-- Index for filtering by status
CREATE INDEX IF NOT EXISTS idx_documents_status 
ON documents(status);

-- Composite index for user + status queries
CREATE INDEX IF NOT EXISTS idx_documents_user_status 
ON documents(user_id, status);

-- Composite index for user + timestamp (for pagination)
CREATE INDEX IF NOT EXISTS idx_documents_user_timestamp 
ON documents(user_id, upload_timestamp DESC);

-- Add comments for documentation
COMMENT ON INDEX idx_documents_user_id IS 'Improves performance for user-specific document queries';
COMMENT ON INDEX idx_documents_upload_timestamp IS 'Improves performance for sorting by upload time';
COMMENT ON INDEX idx_documents_status IS 'Improves performance for filtering by processing status';
COMMENT ON INDEX idx_documents_user_status IS 'Improves performance for user + status filter queries';
COMMENT ON INDEX idx_documents_user_timestamp IS 'Improves performance for user document pagination';
