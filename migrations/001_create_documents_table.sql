-- Create documents table with user association
-- This migration creates the main documents table that stores metadata about uploaded files

CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_path TEXT NOT NULL, -- Supabase Storage path
    storage_url TEXT NOT NULL, -- Public URL from Supabase Storage
    file_size BIGINT NOT NULL,
    content_type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'processing', -- processing, completed, failed
    error_message TEXT,
    chunk_count INTEGER,
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_documents_updated_at 
    BEFORE UPDATE ON documents 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE documents IS 'Stores metadata for uploaded documents with user association';
COMMENT ON COLUMN documents.user_id IS 'References auth.users(id) - the user who uploaded the document';
COMMENT ON COLUMN documents.file_path IS 'Path to file in Supabase Storage';
COMMENT ON COLUMN documents.storage_url IS 'Public URL for accessing the file';
COMMENT ON COLUMN documents.status IS 'Processing status: processing, completed, failed';
