-- Create Row Level Security (RLS) policies for the documents table
-- This migration ensures users can only access their own documents

-- Enable RLS on the documents table
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only insert their own documents
CREATE POLICY "Users can insert their own documents" ON documents
    FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

-- Policy: Users can only view their own documents
CREATE POLICY "Users can view their own documents" ON documents
    FOR SELECT 
    USING (auth.uid() = user_id);

-- Policy: Users can only update their own documents
CREATE POLICY "Users can update their own documents" ON documents
    FOR UPDATE 
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Policy: Users can only delete their own documents
CREATE POLICY "Users can delete their own documents" ON documents
    FOR DELETE 
    USING (auth.uid() = user_id);

-- Create a policy for service role to bypass RLS (for admin operations)
CREATE POLICY "Service role can manage all documents" ON documents
    FOR ALL 
    USING (current_setting('request.jwt.claims', true)::json->>'role' = 'service_role')
    WITH CHECK (current_setting('request.jwt.claims', true)::json->>'role' = 'service_role');

-- Add comments for documentation
COMMENT ON POLICY "Users can insert their own documents" ON documents IS 'Allows users to upload documents associated with their user ID';
COMMENT ON POLICY "Users can view their own documents" ON documents IS 'Restricts document viewing to the owner only';
COMMENT ON POLICY "Users can update their own documents" ON documents IS 'Allows users to update only their own documents';
COMMENT ON POLICY "Users can delete their own documents" ON documents IS 'Allows users to delete only their own documents';
COMMENT ON POLICY "Service role can manage all documents" ON documents IS 'Allows service role to bypass RLS for admin operations';
