#!/usr/bin/env python3
"""
Configuration validation script for RAG Citation API.
Run this script to validate your .env configuration before starting the server.
"""

import os
import sys
from pathlib import Path

def validate_env_file():
    """Validate the .env file exists and has proper format."""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("❌ .env file not found!")
        print("📋 Please copy .env.example to .env and configure your API keys:")
        print("   cp .env.example .env")
        return False
    
    print("✅ .env file found")
    
    # Check for common issues
    issues = []
    with open(env_file, 'r') as f:
        for line_num, line in enumerate(f, 1):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
                
            if '=' in line:
                key, value = line.split('=', 1)
                
                # Check for inline comments
                if '#' in value and not value.startswith('"'):
                    issues.append(f"Line {line_num}: Inline comment detected in {key}={value}")
                
                # Check for placeholder values
                if 'your_' in value.lower() and '_here' in value.lower():
                    issues.append(f"Line {line_num}: Placeholder value detected for {key}")
    
    if issues:
        print("⚠️  Issues found in .env file:")
        for issue in issues:
            print(f"   {issue}")
        return False
    
    print("✅ .env file format looks good")
    return True

def validate_settings():
    """Validate that settings can be loaded properly."""
    try:
        print("🔧 Testing settings import...")
        from app.config import settings
        print("✅ Settings loaded successfully")
        
        # Test key configurations
        print(f"📍 API Host: {settings.app_host}")
        print(f"📍 API Port: {settings.app_port}")
        print(f"📍 Debug Mode: {settings.app_debug}")
        print(f"📍 Upload Directory: {settings.upload_dir}")
        print(f"📍 Max File Size: {settings.max_file_size:,} bytes ({settings.max_file_size / 1024 / 1024:.1f} MB)")
        
        # Check API keys (without revealing them)
        api_keys = {
            'Anthropic': bool(settings.anthropic_api_key and settings.anthropic_api_key != 'your_anthropic_api_key_here'),
            'KDB.AI': bool(settings.kdbai_api_key and settings.kdbai_api_key != 'your_kdbai_api_key_here'),
            'OpenAI': bool(settings.openai_api_key and settings.openai_api_key != 'your_openai_api_key_here'),
        }
        
        print("\n🔑 API Keys Status:")
        for service, configured in api_keys.items():
            status = "✅ Configured" if configured else "❌ Not configured"
            print(f"   {service}: {status}")
        
        # Check upload directory
        if settings.upload_dir.exists():
            print(f"✅ Upload directory exists: {settings.upload_dir}")
        else:
            print(f"📁 Upload directory will be created: {settings.upload_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading settings: {e}")
        print("\n🔍 Common solutions:")
        print("   1. Check for inline comments in .env file")
        print("   2. Ensure all numeric values are valid integers")
        print("   3. Ensure boolean values are true/false")
        print("   4. Check for missing required API keys")
        return False

def validate_imports():
    """Validate that all required packages can be imported."""
    print("\n📦 Testing package imports...")
    
    required_packages = [
        ('fastapi', 'FastAPI'),
        ('uvicorn', 'Uvicorn'),
        ('anthropic', 'Anthropic'),
        ('openai', 'OpenAI'),
        ('langchain', 'LangChain'),
        ('pydantic', 'Pydantic'),
        ('pandas', 'Pandas'),
        ('numpy', 'NumPy'),
    ]
    
    failed_imports = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✅ {name}")
        except ImportError as e:
            print(f"❌ {name}: {e}")
            failed_imports.append(package)
    
    if failed_imports:
        print(f"\n⚠️  Failed to import: {', '.join(failed_imports)}")
        print("💡 Try running: pip install -r requirements.txt")
        return False
    
    return True

def main():
    """Main validation function."""
    print("🔍 RAG Citation API Configuration Validator")
    print("=" * 50)
    
    all_good = True
    
    # Step 1: Validate .env file
    if not validate_env_file():
        all_good = False
    
    # Step 2: Validate package imports
    if not validate_imports():
        all_good = False
    
    # Step 3: Validate settings loading
    if not validate_settings():
        all_good = False
    
    print("\n" + "=" * 50)
    
    if all_good:
        print("🎉 All validations passed! You can start the server with:")
        print("   python run.py")
        print("\n📖 API Documentation will be available at:")
        print("   http://localhost:8000/docs")
    else:
        print("❌ Some validations failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
