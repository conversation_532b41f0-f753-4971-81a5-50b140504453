"""
Tests for the RAG Citation API endpoints.
"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import Mock, patch

from app.main import app

client = TestClient(app)


def test_root_endpoint():
    """Test the root endpoint."""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_health_endpoint():
    """Test the health check endpoint."""
    with patch('app.services.vector_service.VectorService.health_check', return_value=True):
        response = client.get("/api/v1/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "services" in data


def test_list_documents_empty():
    """Test listing documents when none exist."""
    response = client.get("/api/v1/documents")
    assert response.status_code == 200
    data = response.json()
    assert data["total_count"] == 0
    assert data["documents"] == []


@pytest.mark.asyncio
async def test_upload_document():
    """Test document upload endpoint."""
    # Mock file content
    test_content = b"This is a test document content."
    
    with patch('app.services.document_service.DocumentService.validate_file'), \
         patch('app.services.document_service.DocumentService.save_uploaded_file'), \
         patch('app.services.document_service.DocumentService.process_document') as mock_process, \
         patch('app.services.vector_service.VectorService.add_document_chunks'):
        
        # Mock the process_document return value
        mock_document = Mock()
        mock_document.id = "test-id"
        mock_chunks = []
        mock_process.return_value = (mock_document, mock_chunks)
        
        response = client.post(
            "/api/v1/documents/upload",
            files={"file": ("test.txt", test_content, "text/plain")},
            data={"title": "Test Document"}
        )
        
        assert response.status_code == 200


def test_query_no_documents():
    """Test querying when no documents exist."""
    with patch('app.services.embedding_service.EmbeddingService.generate_single_embedding'), \
         patch('app.services.vector_service.VectorService.search_similar_chunks', return_value=[]):
        
        response = client.post(
            "/api/v1/query",
            json={
                "question": "What is this about?",
                "include_citations": True
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "couldn't find any relevant information" in data["answer"].lower()


def test_invalid_file_upload():
    """Test uploading an invalid file."""
    with patch('app.services.document_service.DocumentService.validate_file', 
               side_effect=ValueError("Invalid file type")):
        
        response = client.post(
            "/api/v1/documents/upload",
            files={"file": ("test.exe", b"invalid content", "application/octet-stream")}
        )
        
        assert response.status_code == 400
        assert "Invalid file type" in response.json()["detail"]


def test_get_nonexistent_document():
    """Test getting a document that doesn't exist."""
    response = client.get("/api/v1/documents/00000000-0000-0000-0000-000000000000")
    assert response.status_code == 404


def test_delete_nonexistent_document():
    """Test deleting a document that doesn't exist."""
    response = client.delete("/api/v1/documents/00000000-0000-0000-0000-000000000000")
    assert response.status_code == 404
