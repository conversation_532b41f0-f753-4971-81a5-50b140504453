# RAG Citation API Configuration Template
# Copy this file to .env and fill in your actual values
# 
# IMPORTANT: Do not use inline comments (# text) on the same line as values
# Comments should be on separate lines only

# =============================================================================
# API KEYS (Required)
# =============================================================================

# Anthropic API Key for Citation API
# Get from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# KDB.AI Configuration
# Get from: https://kdb.ai/
KDBAI_ENDPOINT=your_kdbai_endpoint_here
KDBAI_API_KEY=your_kdbai_api_key_here

# OpenAI API Key for embeddings
# Get from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================

# Server host (0.0.0.0 for all interfaces, 127.0.0.1 for localhost only)
APP_HOST=0.0.0.0

# Server port (1-65535)
APP_PORT=8000

# Debug mode (true/false, 1/0, yes/no, on/off)
APP_DEBUG=false

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================

# Directory for uploaded files (relative or absolute path)
UPLOAD_DIR=./uploads

# Maximum file size in bytes (50MB = 50000000)
MAX_FILE_SIZE=50000000

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================

# KDB.AI database name
VECTOR_DB_NAME=rag_documents

# KDB.AI table name for document chunks
VECTOR_TABLE_NAME=document_chunks

# Embedding dimension (1536 for OpenAI text-embedding-3-small)
EMBEDDING_DIMENSION=1536

# Document processing settings
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# =============================================================================
# API METADATA
# =============================================================================

# API title shown in documentation
API_TITLE=RAG Citation API

# API description shown in documentation
API_DESCRIPTION=A REST API for Retrieval-Augmented Generation with Citations

# API version
API_VERSION=1.0.0
