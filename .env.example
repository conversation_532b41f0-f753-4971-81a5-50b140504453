# Anthropic API Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# KDB.AI Configuration
KDBAI_ENDPOINT=your_kdbai_endpoint_here
KDBAI_API_KEY=your_kdbai_api_key_here

# OpenAI Configuration (for embeddings)
OPENAI_API_KEY=your_openai_api_key_here

# Supabase Configuration
SUPABASE_URL=your_supabase_project_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here
SUPABASE_STORAGE_BUCKET=documents

# Database Configuration (for SQLAlchemy/Alembic)
# Get this from Supabase Dashboard > Settings > Database > Connection string
DATABASE_URL=postgresql://postgres:[YOUR_PASSWORD]@db.[PROJECT_REF].supabase.co:5432/postgres

# Application Configuration
APP_HOST=0.0.0.0
APP_PORT=8000
APP_DEBUG=false

# File Storage Configuration (no inline comments)
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=50000000

# Vector Database Configuration
VECTOR_DB_NAME=rag_documents
VECTOR_TABLE_NAME=document_chunks
EMBEDDING_DIMENSION=1536
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# API Configuration
API_TITLE=RAG Citation API
API_DESCRIPTION=A REST API for Retrieval-Augmented Generation with Citations
API_VERSION=1.0.0
