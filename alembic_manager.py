#!/usr/bin/env python3
"""
Alembic migration management script.
Provides a convenient interface for database migrations similar to modern JS/Node.js workflows.
"""
import argparse
import subprocess
import sys
from pathlib import Path

# Load environment variables
from dotenv import load_dotenv
load_dotenv()


def run_command(command: list[str], description: str) -> bool:
    """
    Run a command and handle errors.
    
    Args:
        command: Command to run as list of strings
        description: Description of what the command does
        
    Returns:
        True if successful, False otherwise
    """
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, check=True, capture_output=True, text=True)
        if result.stdout:
            print(result.stdout)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        if e.stdout:
            print("STDOUT:", e.stdout)
        if e.stderr:
            print("STDERR:", e.stderr)
        return False


def check_database_url() -> bool:
    """Check if DATABASE_URL is configured."""
    import os
    database_url = os.getenv('DATABASE_URL')
    if not database_url or 'PLACEHOLDER_PASSWORD' in database_url:
        print("❌ DATABASE_URL is not properly configured.")
        print("\nTo fix this:")
        print("1. Go to your Supabase Dashboard")
        print("2. Navigate to Settings > Database")
        print("3. Copy the 'Connection string' for direct connection")
        print("4. Add it to your .env file as DATABASE_URL=...")
        print("\nExample:")
        print("DATABASE_URL=postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres")
        return False
    return True


def init_migration() -> bool:
    """Initialize migration environment (already done)."""
    if Path("alembic").exists():
        print("✅ Alembic is already initialized")
        return True
    
    return run_command(
        ["alembic", "init", "alembic"],
        "Initializing Alembic migration environment"
    )


def create_migration(message: str, autogenerate: bool = True) -> bool:
    """
    Create a new migration.
    
    Args:
        message: Migration message
        autogenerate: Whether to use autogenerate
        
    Returns:
        True if successful
    """
    if not check_database_url():
        return False
    
    command = ["alembic", "revision"]
    if autogenerate:
        command.append("--autogenerate")
    command.extend(["-m", message])
    
    return run_command(
        command,
        f"Creating migration: {message}"
    )


def upgrade_database(revision: str = "head") -> bool:
    """
    Upgrade database to a specific revision.
    
    Args:
        revision: Target revision (default: head)
        
    Returns:
        True if successful
    """
    if not check_database_url():
        return False
    
    return run_command(
        ["alembic", "upgrade", revision],
        f"Upgrading database to {revision}"
    )


def downgrade_database(revision: str) -> bool:
    """
    Downgrade database to a specific revision.
    
    Args:
        revision: Target revision
        
    Returns:
        True if successful
    """
    if not check_database_url():
        return False
    
    return run_command(
        ["alembic", "downgrade", revision],
        f"Downgrading database to {revision}"
    )


def show_history() -> bool:
    """Show migration history."""
    return run_command(
        ["alembic", "history", "--verbose"],
        "Showing migration history"
    )


def show_current() -> bool:
    """Show current revision."""
    return run_command(
        ["alembic", "current"],
        "Showing current revision"
    )


def show_heads() -> bool:
    """Show head revisions."""
    return run_command(
        ["alembic", "heads"],
        "Showing head revisions"
    )


def main():
    """Main CLI interface."""
    parser = argparse.ArgumentParser(
        description="Alembic migration manager - Modern database migrations for Python",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s generate "Add user table"     # Create new migration with autogenerate
  %(prog)s migrate                       # Apply all pending migrations
  %(prog)s migrate 1234                  # Migrate to specific revision
  %(prog)s rollback -1                   # Rollback one migration
  %(prog)s history                       # Show migration history
  %(prog)s current                       # Show current revision
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Generate migration
    generate_parser = subparsers.add_parser('generate', help='Generate new migration')
    generate_parser.add_argument('message', help='Migration message')
    generate_parser.add_argument('--manual', action='store_true', help='Create empty migration (no autogenerate)')
    
    # Migrate (upgrade)
    migrate_parser = subparsers.add_parser('migrate', help='Apply migrations')
    migrate_parser.add_argument('revision', nargs='?', default='head', help='Target revision (default: head)')
    
    # Rollback (downgrade)
    rollback_parser = subparsers.add_parser('rollback', help='Rollback migrations')
    rollback_parser.add_argument('revision', help='Target revision (e.g., -1, base, or specific revision)')
    
    # History
    subparsers.add_parser('history', help='Show migration history')
    
    # Current
    subparsers.add_parser('current', help='Show current revision')
    
    # Heads
    subparsers.add_parser('heads', help='Show head revisions')
    
    # Status (combination of current and heads)
    subparsers.add_parser('status', help='Show migration status')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🗄️  Alembic Migration Manager")
    print("=" * 50)
    
    success = True
    
    if args.command == 'generate':
        success = create_migration(args.message, autogenerate=not args.manual)
    
    elif args.command == 'migrate':
        success = upgrade_database(args.revision)
    
    elif args.command == 'rollback':
        success = downgrade_database(args.revision)
    
    elif args.command == 'history':
        success = show_history()
    
    elif args.command == 'current':
        success = show_current()
    
    elif args.command == 'heads':
        success = show_heads()
    
    elif args.command == 'status':
        print("Current revision:")
        show_current()
        print("\nHead revisions:")
        show_heads()
        success = True
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
