#!/bin/bash

# RAG Citation API Installation Script
# This script handles Python 3.12.8 compatibility issues

set -e  # Exit on any error

echo "🚀 Starting RAG Citation API installation..."

# Check Python version
PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
echo "📍 Python version: $PYTHON_VERSION"

if [[ ! "$PYTHON_VERSION" =~ ^3\.(11|12) ]]; then
    echo "⚠️  Warning: This project is tested with Python 3.11+ and 3.12+"
    echo "   Your version: $PYTHON_VERSION"
    read -p "   Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Step 1: Upgrade core tools
echo "🔧 Step 1: Upgrading core Python tools..."
python -m pip install --upgrade pip
pip install --upgrade setuptools wheel

# Step 2: Install core dependencies
echo "📦 Step 2: Installing core dependencies..."
pip install -r requirements-core.txt

# Step 3: Install ML dependencies
echo "🤖 Step 3: Installing ML/AI dependencies..."
pip install -r requirements-ml.txt

# Step 4: Install development dependencies (optional)
echo "🛠️  Step 4: Installing development dependencies..."
pip install pytest>=7.4.3,\<8.0.0
pip install pytest-asyncio>=0.21.1,\<1.0.0
pip install black>=23.12.1,\<25.0.0
pip install isort>=5.13.2,\<6.0.0

# Step 5: Verify installation
echo "✅ Step 5: Verifying installation..."
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')"
python -c "import anthropic; print(f'Anthropic: {anthropic.__version__}')"
python -c "import openai; print(f'OpenAI: {openai.__version__}')"

echo "🎉 Installation completed successfully!"
echo ""
echo "Next steps:"
echo "1. Copy .env.example to .env and configure your API keys"
echo "2. Run: python run.py"
echo "3. Visit: http://localhost:8000/docs"
