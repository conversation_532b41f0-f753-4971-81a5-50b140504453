"""
API endpoints for the RAG Citation API.
"""
import logging
import time
from typing import List
from uuid import UUID

from fastapi import APIRouter, Depends, File, Form, HTTPException, UploadFile, status
from fastapi.responses import J<PERSON><PERSON>esponse

from app.auth.dependencies import get_current_user, get_current_user_id
from app.models import (
    Document,
    DocumentListResponse,
    DocumentUploadResponse,
    ErrorResponse,
    HealthResponse,
    QueryRequest,
    QueryResponse,
    User,
)
from app.services.citation_service import CitationService
from app.services.document_service import DocumentService
from app.services.embedding_service import EmbeddingService
from app.services.vector_service import VectorService
from app.config import settings

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Service instances (will be injected as dependencies)
embedding_service = EmbeddingService()
vector_service = VectorService()
document_service = DocumentService(embedding_service)
citation_service = CitationService()


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    try:
        # Check vector database connection
        vector_db_status = "healthy" if vector_service.health_check() else "unhealthy"

        return HealthResponse(
            status="healthy",
            version=settings.api_version,
            services={
                "vector_database": vector_db_status,
                "anthropic_api": "healthy",  # Assume healthy if no errors
                "openai_api": "healthy"      # Assume healthy if no errors
            }
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service unhealthy"
        )


@router.post("/documents/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    title: str = Form(None),
    user_id: UUID = Depends(get_current_user_id)
):
    """
    Upload and process a document.

    Args:
        file: Uploaded file (PDF, TXT, or MD)
        title: Optional custom title for the document
        user_id: Current authenticated user ID

    Returns:
        Document upload response with processing status
    """
    try:
        logger.info(f"Uploading document for user {user_id}: {file.filename}")

        # Validate file
        if not file.filename:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No filename provided"
            )

        # Read file content
        file_content = await file.read()

        # Upload and process document
        document, chunks = await document_service.upload_and_process_document(
            user_id=user_id,
            file_content=file_content,
            original_filename=title or file.filename,
            content_type=file.content_type or "application/octet-stream"
        )

        # Add chunks to vector database
        await vector_service.add_document_chunks(chunks)

        logger.info(f"Successfully uploaded and processed document: {document.id}")

        return DocumentUploadResponse(
            document=document,
            message=f"Document uploaded and processed successfully. Created {len(chunks)} chunks."
        )

    except ValueError as e:
        logger.warning(f"Validation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error uploading document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload and process document"
        )


@router.get("/documents", response_model=DocumentListResponse)
async def list_documents(
    user_id: UUID = Depends(get_current_user_id),
    limit: int = 50,
    offset: int = 0
):
    """
    List uploaded documents for the current user.

    Args:
        user_id: Current authenticated user ID
        limit: Maximum number of documents to return
        offset: Number of documents to skip

    Returns:
        List of documents with metadata
    """
    try:
        documents = await document_service.list_user_documents(user_id, limit, offset)

        return DocumentListResponse(
            documents=documents,
            total_count=len(documents)
        )

    except Exception as e:
        logger.error(f"Error listing documents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list documents"
        )


@router.get("/documents/{document_id}", response_model=Document)
async def get_document(
    document_id: UUID,
    user_id: UUID = Depends(get_current_user_id)
):
    """
    Get a specific document by ID for the current user.

    Args:
        document_id: Document UUID
        user_id: Current authenticated user ID

    Returns:
        Document details
    """
    try:
        document = await document_service.get_document(document_id, user_id)

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )

        return document

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get document"
        )


@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: UUID,
    user_id: UUID = Depends(get_current_user_id)
):
    """
    Delete a document and its associated chunks for the current user.

    Args:
        document_id: Document UUID to delete
        user_id: Current authenticated user ID

    Returns:
        Success message
    """
    try:
        # Delete from vector database
        await vector_service.delete_document_chunks(document_id)

        # Delete document and file
        success = await document_service.delete_document(document_id, user_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )

        return {"message": f"Document {document_id} deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document"
        )


@router.post("/query", response_model=QueryResponse)
async def query_documents(
    request: QueryRequest,
    user_id: UUID = Depends(get_current_user_id)
):
    """
    Query documents and get an answer with citations.

    Args:
        request: Query request with question and optional filters

    Returns:
        Answer with citations and source documents
    """
    try:
        start_time = time.time()

        logger.info(f"Processing query: {request.question[:100]}...")

        # Generate query embedding
        query_embedding = await embedding_service.generate_single_embedding(request.question)

        # Search for similar chunks
        similar_chunks = await vector_service.search_similar_chunks(
            query_embedding=query_embedding,
            document_ids=request.document_ids,
            limit=request.max_results
        )

        if not similar_chunks:
            return QueryResponse(
                answer="I couldn't find any relevant information in the uploaded documents to answer your question.",
                content_blocks=[],
                sources=[],
                processing_time=time.time() - start_time
            )

        # Get source documents (only user's documents)
        source_doc_ids = list(set(chunk.document_id for chunk, _ in similar_chunks))
        user_documents = await document_service.list_user_documents(user_id)
        source_documents = [
            doc for doc in user_documents
            if doc.id in source_doc_ids
        ]

        # Create document title mapping
        document_titles = {str(doc.id): doc.filename for doc in source_documents}

        # Generate response with citations
        if request.include_citations:
            try:
                answer, content_blocks = await citation_service.generate_cited_response(
                    question=request.question,
                    relevant_chunks=similar_chunks,
                    document_titles=document_titles
                )
            except Exception as e:
                logger.warning(f"Citation generation failed, falling back to simple response: {str(e)}")
                # Fallback to simple response
                answer = await citation_service.generate_simple_response(
                    question=request.question,
                    context_chunks=[chunk for chunk, _ in similar_chunks]
                )
                content_blocks = []
        else:
            # Generate simple response without citations
            answer = await citation_service.generate_simple_response(
                question=request.question,
                context_chunks=[chunk for chunk, _ in similar_chunks]
            )
            content_blocks = []

        processing_time = time.time() - start_time

        logger.info(f"Query processed successfully in {processing_time:.2f}s")

        return QueryResponse(
            answer=answer,
            content_blocks=content_blocks,
            sources=source_documents,
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"Error processing query: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to process query"
        )
