"""
Pydantic models for the RAG Citation API.
"""
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from pydantic import BaseModel, Field


class DocumentMetadata(BaseModel):
    """Metadata for uploaded documents."""
    filename: str
    file_size: int
    content_type: str
    upload_timestamp: datetime
    chunk_count: Optional[int] = None


class Document(BaseModel):
    """Document model."""
    id: UUID = Field(default_factory=uuid4)
    user_id: UUID
    filename: str
    original_filename: str
    file_path: str  # Supabase Storage path
    storage_url: str  # Public URL from Supabase Storage
    metadata: DocumentMetadata
    status: str = "processing"  # processing, completed, failed
    error_message: Optional[str] = None


class DocumentChunk(BaseModel):
    """Document chunk model for vector storage."""
    id: str
    document_id: UUID
    chunk_index: int
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None


class Citation(BaseModel):
    """Citation model based on Anthropic's citation format."""
    type: str  # "char_location", "page_location", "content_block_location"
    cited_text: str
    document_index: int
    document_title: str
    start_char_index: Optional[int] = None
    end_char_index: Optional[int] = None
    start_page_number: Optional[int] = None
    end_page_number: Optional[int] = None
    start_block_index: Optional[int] = None
    end_block_index: Optional[int] = None


class TextBlock(BaseModel):
    """Text block with optional citations."""
    type: str = "text"
    text: str
    citations: Optional[List[Citation]] = None


class QueryRequest(BaseModel):
    """Request model for querying documents."""
    question: str = Field(..., min_length=1, max_length=1000)
    document_ids: Optional[List[UUID]] = None
    max_results: int = Field(default=5, ge=1, le=20)
    include_citations: bool = Field(default=True)


class QueryResponse(BaseModel):
    """Response model for document queries."""
    answer: str
    content_blocks: List[TextBlock]
    sources: List[Document]
    processing_time: float


class DocumentUploadResponse(BaseModel):
    """Response model for document upload."""
    document: Document
    message: str


class DocumentListResponse(BaseModel):
    """Response model for listing documents."""
    documents: List[Document]
    total_count: int


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    detail: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.utcnow)


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    version: str
    services: Dict[str, str]


class User(BaseModel):
    """User model from Supabase Auth."""
    id: UUID
    email: str
    created_at: datetime
    updated_at: datetime
