"""
Authentication dependencies for FastAPI endpoints.
"""
import logging
from typing import Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTP<PERSON>earer
from supabase import Client, create_client

from app.config import settings
from app.models import User

logger = logging.getLogger(__name__)

# HTTP Bearer token scheme
security = HTTPBearer()


class AuthService:
    """Service for handling authentication with Supabase."""
    
    def __init__(self):
        self.client: Client = create_client(
            settings.supabase_url,
            settings.supabase_anon_key  # Use anon key for auth verification
        )
    
    async def verify_token(self, token: str) -> Optional[User]:
        """
        Verify JWT token and return user information.
        
        Args:
            token: JWT token from Authorization header
            
        Returns:
            User object if token is valid, None otherwise
        """
        try:
            # Verify token with Supabase
            response = self.client.auth.get_user(token)
            
            if response.user is None:
                return None
            
            user_data = response.user
            
            # Create User model
            user = User(
                id=UUID(user_data.id),
                email=user_data.email,
                created_at=user_data.created_at,
                updated_at=user_data.updated_at
            )
            
            return user
            
        except Exception as e:
            logger.warning(f"Token verification failed: {str(e)}")
            return None


# Global auth service instance
auth_service = AuthService()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> User:
    """
    Dependency to get the current authenticated user.
    
    Args:
        credentials: HTTP Bearer credentials
        
    Returns:
        Current user
        
    Raises:
        HTTPException: If authentication fails
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing authentication token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    user = await auth_service.verify_token(credentials.credentials)
    
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    return user


async def get_current_user_optional(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[User]:
    """
    Dependency to get the current user if authenticated, None otherwise.
    
    Args:
        credentials: Optional HTTP Bearer credentials
        
    Returns:
        Current user if authenticated, None otherwise
    """
    if not credentials:
        return None
    
    user = await auth_service.verify_token(credentials.credentials)
    return user


async def get_current_user_id(current_user: User = Depends(get_current_user)) -> UUID:
    """
    Dependency to get the current user's ID.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        User ID
    """
    return current_user.id
