"""
Configuration settings for the RAG Citation API.
"""
import os
from pathlib import Path
from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    # API Configuration
    app_host: str = Field(default="0.0.0.0", env="APP_HOST")
    app_port: int = Field(default=8000, env="APP_PORT")
    app_debug: bool = Field(default=False, env="APP_DEBUG")

    # API Metadata
    api_title: str = Field(default="RAG Citation API", env="API_TITLE")
    api_description: str = Field(
        default="A REST API for Retrieval-Augmented Generation with Citations",
        env="API_DESCRIPTION"
    )
    api_version: str = Field(default="1.0.0", env="API_VERSION")

    # Anthropic Configuration
    anthropic_api_key: str = Field(..., env="ANTHROPIC_API_KEY")

    # KDB.AI Configuration
    kdbai_endpoint: str = Field(..., env="KDBAI_ENDPOINT")
    kdbai_api_key: str = Field(..., env="KDBAI_API_KEY")

    # OpenAI Configuration
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")

    # Supabase Configuration
    supabase_url: str = Field(..., env="SUPABASE_URL")
    supabase_anon_key: str = Field(..., env="SUPABASE_ANON_KEY")
    supabase_service_role_key: str = Field(..., env="SUPABASE_SERVICE_ROLE_KEY")
    supabase_storage_bucket: str = Field(default="documents", env="SUPABASE_STORAGE_BUCKET")

    # File Storage Configuration
    upload_dir: Path = Field(default=Path("./uploads"), env="UPLOAD_DIR")
    max_file_size: int = Field(default=50_000_000, env="MAX_FILE_SIZE")  # 50MB

    # Vector Database Configuration
    vector_db_name: str = Field(default="rag_documents", env="VECTOR_DB_NAME")
    vector_table_name: str = Field(default="document_chunks", env="VECTOR_TABLE_NAME")
    embedding_dimension: int = Field(default=1536, env="EMBEDDING_DIMENSION")

    # Document Processing Configuration
    chunk_size: int = Field(default=1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(default=200, env="CHUNK_OVERLAP")

    # Supported file types
    supported_file_types: set[str] = {".pdf", ".txt", ".md"}

    @field_validator('max_file_size', mode='before')
    @classmethod
    def parse_file_size(cls, v):
        """Parse file size, handling potential string values with comments."""
        if isinstance(v, str):
            # Remove any inline comments and whitespace
            v = v.split('#')[0].strip()
            try:
                return int(v)
            except ValueError:
                raise ValueError(f"Invalid file size value: {v}. Must be a valid integer.")
        return v

    @field_validator('app_port', mode='before')
    @classmethod
    def parse_port(cls, v):
        """Parse port number, handling potential string values."""
        if isinstance(v, str):
            v = v.split('#')[0].strip()
            try:
                port = int(v)
                if not (1 <= port <= 65535):
                    raise ValueError(f"Port must be between 1 and 65535, got {port}")
                return port
            except ValueError as e:
                if "Port must be between" in str(e):
                    raise e
                raise ValueError(f"Invalid port value: {v}. Must be a valid integer.")
        return v

    @field_validator('app_debug', mode='before')
    @classmethod
    def parse_debug(cls, v):
        """Parse debug flag, handling string boolean values."""
        if isinstance(v, str):
            v = v.split('#')[0].strip().lower()
            if v in ('true', '1', 'yes', 'on'):
                return True
            elif v in ('false', '0', 'no', 'off'):
                return False
            else:
                raise ValueError(f"Invalid debug value: {v}. Must be true/false.")
        return v

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False
        # Allow extra fields for flexibility
        extra = "ignore"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # Ensure upload directory exists
        self.upload_dir.mkdir(parents=True, exist_ok=True)


# Global settings instance
settings = Settings()
