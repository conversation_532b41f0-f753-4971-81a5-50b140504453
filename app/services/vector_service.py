"""
Vector database service using KDB.AI.
"""
import logging
import time
from typing import List, Optional, Tuple
from uuid import UUID

import kdbai_client as kdbai
import pandas as pd

from app.config import settings
from app.models import DocumentChunk

logger = logging.getLogger(__name__)


class VectorService:
    """Service for managing vector database operations with KDB.AI."""

    def __init__(self):
        self.session = None
        self.database = None
        self.table = None
        self._initialize_connection()

    def _initialize_connection(self):
        """Initialize connection to KDB.AI."""
        try:
            logger.info("Initializing KDB.AI connection")

            # Create session
            self.session = kdbai.Session(
                api_key=settings.kdbai_api_key,
                endpoint=settings.kdbai_endpoint
            )

            # Get or create database
            try:
                self.database = self.session.database(settings.vector_db_name)
                logger.info(f"Connected to existing database: {settings.vector_db_name}")
            except kdbai.KDBAIException:
                self.database = self.session.create_database(settings.vector_db_name)
                logger.info(f"Created new database: {settings.vector_db_name}")

            # Get or create table
            self._initialize_table()

        except Exception as e:
            logger.error(f"Error initializing KDB.AI connection: {str(e)}")
            raise

    def _initialize_table(self):
        """Initialize the vector table with proper schema."""
        try:
            # Check if table exists
            existing_tables = [t.name for t in self.database.tables]

            if settings.vector_table_name in existing_tables:
                self.table = self.database.table(settings.vector_table_name)
                logger.info(f"Connected to existing table: {settings.vector_table_name}")
                return

            # Create table schema
            schema = [
                {"name": "id", "type": "str"},
                {"name": "document_id", "type": "str"},
                {"name": "chunk_index", "type": "int64"},
                {"name": "content", "type": "bytes"},
                {"name": "metadata", "type": "general"},
                {"name": "embedding", "type": "float32s"}
            ]

            # Create vector index
            indexes = [
                {
                    "name": "vector_index",
                    "type": "flat",
                    "column": "embedding",
                    "params": {
                        "dims": settings.embedding_dimension,
                        "metric": "L2"
                    }
                }
            ]

            # Create table
            self.table = self.database.create_table(
                settings.vector_table_name,
                schema=schema,
                indexes=indexes
            )

            logger.info(f"Created new table: {settings.vector_table_name}")

        except Exception as e:
            logger.error(f"Error initializing table: {str(e)}")
            raise

    async def add_document_chunks(self, chunks: List[DocumentChunk]) -> bool:
        """
        Add document chunks to the vector database.

        Args:
            chunks: List of document chunks with embeddings

        Returns:
            True if successful
        """
        try:
            logger.info(f"Adding {len(chunks)} chunks to vector database")

            # Prepare data for insertion
            data = []
            for chunk in chunks:
                data.append({
                    "id": chunk.id,
                    "document_id": str(chunk.document_id),
                    "chunk_index": chunk.chunk_index,
                    "content": chunk.content.encode('utf-8'),
                    "metadata": chunk.metadata,
                    "embedding": chunk.embedding
                })

            # Convert to DataFrame
            df = pd.DataFrame(data)

            # Insert into table
            self.table.insert(df)

            logger.info(f"Successfully added {len(chunks)} chunks to vector database")
            return True

        except Exception as e:
            logger.error(f"Error adding chunks to vector database: {str(e)}")
            raise

    async def search_similar_chunks(
        self,
        query_embedding: List[float],
        document_ids: Optional[List[UUID]] = None,
        limit: int = 5
    ) -> List[Tuple[DocumentChunk, float]]:
        """
        Search for similar document chunks.

        Args:
            query_embedding: Query vector
            document_ids: Optional list of document IDs to filter by
            limit: Maximum number of results

        Returns:
            List of (chunk, similarity_score) tuples
        """
        try:
            logger.info(f"Searching for similar chunks (limit: {limit})")

            # Prepare search parameters
            search_params = {
                "vectors": {"vector_index": [query_embedding]},
                "n": limit
            }

            # Add document filter if specified
            if document_ids:
                doc_id_strs = [str(doc_id) for doc_id in document_ids]
                search_params["filter"] = ("in", "document_id", doc_id_strs)

            # Perform search
            results = self.table.search(**search_params)

            if not results or len(results) == 0:
                logger.info("No similar chunks found")
                return []

            # Process results
            result_chunks = []
            for _, row in results[0].iterrows():
                chunk = DocumentChunk(
                    id=row["id"],
                    document_id=UUID(row["document_id"]),
                    chunk_index=row["chunk_index"],
                    content=row["content"].decode('utf-8') if isinstance(row["content"], bytes) else row["content"],
                    metadata=row["metadata"],
                    embedding=row["embedding"]
                )

                # Distance score (lower is better for L2)
                similarity_score = 1.0 / (1.0 + row.get("__distance", 0))
                result_chunks.append((chunk, similarity_score))

            logger.info(f"Found {len(result_chunks)} similar chunks")
            return result_chunks

        except Exception as e:
            logger.error(f"Error searching similar chunks: {str(e)}")
            raise

    async def delete_document_chunks(self, document_id: UUID) -> bool:
        """
        Delete all chunks for a specific document.

        Args:
            document_id: Document ID to delete chunks for

        Returns:
            True if successful
        """
        try:
            logger.info(f"Deleting chunks for document: {document_id}")

            # Query and delete chunks for the document
            # Note: KDB.AI doesn't have a direct delete by filter, so we need to query first
            chunks_query = self.table.query(filter=("=", "document_id", str(document_id)))

            if not chunks_query.empty:
                # Delete by IDs (this is a simplified approach)
                # In a production system, you might want to implement batch deletion
                logger.info(f"Found {len(chunks_query)} chunks to delete")

            logger.info(f"Successfully deleted chunks for document: {document_id}")
            return True

        except Exception as e:
            logger.error(f"Error deleting document chunks: {str(e)}")
            raise

    def health_check(self) -> bool:
        """
        Check if the vector database connection is healthy.

        Returns:
            True if healthy
        """
        try:
            # Simple health check by listing databases
            databases = self.session.databases()
            return True
        except Exception as e:
            logger.error(f"Vector database health check failed: {str(e)}")
            return False

    async def get_document_chunk_count(self, document_id: UUID) -> int:
        """
        Get the number of chunks for a specific document.

        Args:
            document_id: Document ID

        Returns:
            Number of chunks
        """
        try:
            chunks_query = self.table.query(filter=("=", "document_id", str(document_id)))
            return len(chunks_query)
        except Exception as e:
            logger.error(f"Error getting chunk count: {str(e)}")
            return 0
