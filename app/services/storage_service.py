"""
Supabase Storage service for file management.
"""
import logging
from pathlib import Path
from typing import <PERSON>ple
from uuid import UUID, uuid4

from supabase import Client, create_client

from app.config import settings

logger = logging.getLogger(__name__)


class StorageService:
    """Service for managing files in Supabase Storage."""

    def __init__(self):
        self.client: Client = create_client(
            settings.supabase_url,
            settings.supabase_service_role_key  # Use service role for server-side operations
        )
        self.bucket_name = settings.supabase_storage_bucket

    async def upload_file(
        self,
        user_id: UUID,
        file_content: bytes,
        original_filename: str
    ) -> Tuple[str, str, str]:
        """
        Upload a file to Supabase Storage.

        Args:
            user_id: ID of the user uploading the file
            file_content: File content as bytes
            original_filename: Original filename

        Returns:
            Tuple of (file_path, storage_url, processed_filename)
        """
        try:
            logger.info(f"Uploading file for user {user_id}: {original_filename}")

            # Generate unique filename
            file_id = uuid4()
            file_extension = Path(original_filename).suffix
            processed_filename = f"{file_id}{file_extension}"

            # Create file path with user isolation
            file_path = f"documents/{user_id}/{processed_filename}"

            # Upload file to Supabase Storage
            result = self.client.storage.from_(self.bucket_name).upload(
                path=file_path,
                file=file_content,
                file_options={
                    "content-type": self._get_content_type(file_extension),
                    "cache-control": "3600"
                }
            )

            if result.error:
                raise Exception(f"Storage upload failed: {result.error}")

            # Get public URL
            storage_url = self.client.storage.from_(self.bucket_name).get_public_url(file_path)

            logger.info(f"Successfully uploaded file: {file_path}")
            return file_path, storage_url, processed_filename

        except Exception as e:
            logger.error(f"Error uploading file: {str(e)}")
            raise

    async def delete_file(self, file_path: str) -> bool:
        """
        Delete a file from Supabase Storage.

        Args:
            file_path: Path to the file in storage

        Returns:
            True if successful
        """
        try:
            logger.info(f"Deleting file: {file_path}")

            result = self.client.storage.from_(self.bucket_name).remove([file_path])

            if result.error:
                logger.error(f"Storage delete failed: {result.error}")
                return False

            logger.info(f"Successfully deleted file: {file_path}")
            return True

        except Exception as e:
            logger.error(f"Error deleting file: {str(e)}")
            return False

    async def get_file_url(self, file_path: str, expires_in: int = 3600) -> str:
        """
        Get a signed URL for a file.

        Args:
            file_path: Path to the file in storage
            expires_in: URL expiration time in seconds

        Returns:
            Signed URL for the file
        """
        try:
            # For private buckets, use signed URL
            result = self.client.storage.from_(self.bucket_name).create_signed_url(
                path=file_path,
                expires_in=expires_in
            )

            if result.error:
                raise Exception(f"Failed to create signed URL: {result.error}")

            return result.signed_url

        except Exception as e:
            logger.error(f"Error creating signed URL: {str(e)}")
            # Fallback to public URL
            return self.client.storage.from_(self.bucket_name).get_public_url(file_path)

    async def download_file(self, file_path: str) -> bytes:
        """
        Download a file from Supabase Storage.

        Args:
            file_path: Path to the file in storage

        Returns:
            File content as bytes
        """
        try:
            logger.info(f"Downloading file: {file_path}")

            result = self.client.storage.from_(self.bucket_name).download(file_path)

            if result.error:
                raise Exception(f"Storage download failed: {result.error}")

            logger.info(f"Successfully downloaded file: {file_path}")
            return result.data

        except Exception as e:
            logger.error(f"Error downloading file: {str(e)}")
            raise

    def _get_content_type(self, file_extension: str) -> str:
        """
        Get MIME content type based on file extension.

        Args:
            file_extension: File extension (e.g., '.pdf')

        Returns:
            MIME content type
        """
        content_types = {
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.md': 'text/markdown',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }

        return content_types.get(file_extension.lower(), 'application/octet-stream')

    async def create_bucket_if_not_exists(self) -> bool:
        """
        Create the storage bucket if it doesn't exist.

        Returns:
            True if bucket exists or was created successfully
        """
        try:
            # Check if bucket exists
            buckets_response = self.client.storage.list_buckets()

            # Handle different response formats
            if hasattr(buckets_response, 'data'):
                buckets = buckets_response.data
            elif isinstance(buckets_response, list):
                buckets = buckets_response
            else:
                buckets = []

            bucket_exists = any(
                (hasattr(bucket, 'name') and bucket.name == self.bucket_name) or
                (hasattr(bucket, 'id') and bucket.id == self.bucket_name) or
                (isinstance(bucket, dict) and (bucket.get('name') == self.bucket_name or bucket.get('id') == self.bucket_name))
                for bucket in buckets
            )

            if bucket_exists:
                logger.info(f"Storage bucket '{self.bucket_name}' already exists")
                return True

            # Create bucket
            result = self.client.storage.create_bucket(
                id=self.bucket_name,
                name=self.bucket_name,
                options={
                    "public": False,  # Private bucket for security
                    "file_size_limit": settings.max_file_size,
                    "allowed_mime_types": [
                        "application/pdf",
                        "text/plain",
                        "text/markdown",
                        "application/msword",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    ]
                }
            )

            # Handle different response formats
            if hasattr(result, 'error') and result.error:
                logger.error(f"Failed to create bucket: {result.error}")
                return False
            elif isinstance(result, dict) and result.get('error'):
                logger.error(f"Failed to create bucket: {result['error']}")
                return False

            logger.info(f"Successfully created storage bucket: {self.bucket_name}")
            return True

        except Exception as e:
            logger.error(f"Error creating bucket: {str(e)}")
            logger.info("You may need to create the storage bucket manually in the Supabase dashboard")
            return False
