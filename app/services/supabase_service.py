"""
Supabase database service for document management.
"""
import logging
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from supabase import Client, create_client

from app.config import settings
from app.models import Document, DocumentMetadata

logger = logging.getLogger(__name__)


class SupabaseService:
    """Service for managing documents in Supabase PostgreSQL database."""

    def __init__(self):
        self.client: Client = create_client(
            settings.supabase_url,
            settings.supabase_service_role_key  # Use service role for server-side operations
        )

    async def create_document(
        self,
        user_id: UUID,
        filename: str,
        original_filename: str,
        file_path: str,
        storage_url: str,
        file_size: int,
        content_type: str
    ) -> Document:
        """
        Create a new document record in the database.

        Args:
            user_id: ID of the user uploading the document
            filename: Processed filename
            original_filename: Original filename from upload
            file_path: Path in Supabase Storage
            storage_url: Public URL for the file
            file_size: Size of the file in bytes
            content_type: MIME type of the file

        Returns:
            Created document
        """
        try:
            logger.info(f"Creating document record for user {user_id}: {filename}")

            # Create document metadata
            metadata = DocumentMetadata(
                filename=original_filename,
                file_size=file_size,
                content_type=content_type,
                upload_timestamp=datetime.utcnow()
            )

            # Insert document record
            result = self.client.table("documents").insert({
                "user_id": str(user_id),
                "filename": filename,
                "original_filename": original_filename,
                "file_path": file_path,
                "storage_url": storage_url,
                "file_size": file_size,
                "content_type": content_type,
                "status": "processing"
            }).execute()

            if not result.data:
                raise Exception("Failed to create document record")

            document_data = result.data[0]

            # Create Document model
            document = Document(
                id=UUID(document_data["id"]),
                user_id=UUID(document_data["user_id"]),
                filename=document_data["filename"],
                original_filename=document_data["original_filename"],
                file_path=document_data["file_path"],
                storage_url=document_data["storage_url"],
                metadata=metadata,
                status=document_data["status"],
                error_message=document_data.get("error_message")
            )

            logger.info(f"Successfully created document record: {document.id}")
            return document

        except Exception as e:
            logger.error(f"Error creating document record: {str(e)}")
            raise

    async def get_document(self, document_id: UUID, user_id: UUID) -> Optional[Document]:
        """
        Get a document by ID for a specific user.

        Args:
            document_id: Document ID
            user_id: User ID (for RLS)

        Returns:
            Document if found, None otherwise
        """
        try:
            # Set RLS context for the user
            self.client.auth.set_session(None)  # Clear any existing session

            result = self.client.table("documents").select("*").eq(
                "id", str(document_id)
            ).eq("user_id", str(user_id)).execute()

            if not result.data:
                return None

            document_data = result.data[0]

            # Create metadata
            metadata = DocumentMetadata(
                filename=document_data["original_filename"],
                file_size=document_data["file_size"],
                content_type=document_data["content_type"],
                upload_timestamp=datetime.fromisoformat(
                    document_data["upload_timestamp"].replace('Z', '+00:00')
                ),
                chunk_count=document_data.get("chunk_count")
            )

            # Create Document model
            document = Document(
                id=UUID(document_data["id"]),
                user_id=UUID(document_data["user_id"]),
                filename=document_data["filename"],
                original_filename=document_data["original_filename"],
                file_path=document_data["file_path"],
                storage_url=document_data["storage_url"],
                metadata=metadata,
                status=document_data["status"],
                error_message=document_data.get("error_message")
            )

            return document

        except Exception as e:
            logger.error(f"Error getting document: {str(e)}")
            raise

    async def list_user_documents(self, user_id: UUID, limit: int = 50, offset: int = 0) -> List[Document]:
        """
        List documents for a specific user.

        Args:
            user_id: User ID
            limit: Maximum number of documents to return
            offset: Number of documents to skip

        Returns:
            List of documents
        """
        try:
            result = self.client.table("documents").select("*").eq(
                "user_id", str(user_id)
            ).order("upload_timestamp", desc=True).range(offset, offset + limit - 1).execute()

            documents = []
            for document_data in result.data:
                # Create metadata
                metadata = DocumentMetadata(
                    filename=document_data["original_filename"],
                    file_size=document_data["file_size"],
                    content_type=document_data["content_type"],
                    upload_timestamp=datetime.fromisoformat(
                        document_data["upload_timestamp"].replace('Z', '+00:00')
                    ),
                    chunk_count=document_data.get("chunk_count")
                )

                # Create Document model
                document = Document(
                    id=UUID(document_data["id"]),
                    user_id=UUID(document_data["user_id"]),
                    filename=document_data["filename"],
                    original_filename=document_data["original_filename"],
                    file_path=document_data["file_path"],
                    storage_url=document_data["storage_url"],
                    metadata=metadata,
                    status=document_data["status"],
                    error_message=document_data.get("error_message")
                )

                documents.append(document)

            logger.info(f"Retrieved {len(documents)} documents for user {user_id}")
            return documents

        except Exception as e:
            logger.error(f"Error listing user documents: {str(e)}")
            raise

    async def update_document_status(
        self,
        document_id: UUID,
        status: str,
        error_message: Optional[str] = None,
        chunk_count: Optional[int] = None
    ) -> bool:
        """
        Update document processing status.

        Args:
            document_id: Document ID
            status: New status (processing, completed, failed)
            error_message: Error message if status is failed
            chunk_count: Number of chunks created

        Returns:
            True if successful
        """
        try:
            update_data = {"status": status}

            if error_message is not None:
                update_data["error_message"] = error_message

            if chunk_count is not None:
                update_data["chunk_count"] = chunk_count

            result = self.client.table("documents").update(update_data).eq(
                "id", str(document_id)
            ).execute()

            success = len(result.data) > 0
            if success:
                logger.info(f"Updated document {document_id} status to {status}")
            else:
                logger.warning(f"No document found with ID {document_id}")

            return success

        except Exception as e:
            logger.error(f"Error updating document status: {str(e)}")
            raise

    async def delete_document(self, document_id: UUID, user_id: UUID) -> bool:
        """
        Delete a document record.

        Args:
            document_id: Document ID
            user_id: User ID (for RLS)

        Returns:
            True if successful
        """
        try:
            result = self.client.table("documents").delete().eq(
                "id", str(document_id)
            ).eq("user_id", str(user_id)).execute()

            success = len(result.data) > 0
            if success:
                logger.info(f"Deleted document record: {document_id}")
            else:
                logger.warning(f"No document found with ID {document_id} for user {user_id}")

            return success

        except Exception as e:
            logger.error(f"Error deleting document: {str(e)}")
            raise
