"""
Document processing service using LangChain.
"""
import logging
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Tuple
from uuid import UUID, uuid4

import aiofiles
from langchain_community.document_loaders import <PERSON>y<PERSON><PERSON>oa<PERSON>, TextLoader
from langchain_text_splitters import RecursiveCharacterTextSplitter

from app.config import settings
from app.models import Document, DocumentChunk, DocumentMetadata
from app.services.embedding_service import EmbeddingService
from app.services.storage_service import StorageService
from app.services.supabase_service import SupabaseService

logger = logging.getLogger(__name__)


class DocumentService:
    """Service for processing and managing documents."""

    def __init__(self, embedding_service: EmbeddingService):
        self.embedding_service = embedding_service
        self.storage_service = StorageService()
        self.supabase_service = SupabaseService()
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=settings.chunk_size,
            chunk_overlap=settings.chunk_overlap,
            separators=["\n\n", "\n", " ", ""]
        )

    async def upload_and_process_document(
        self,
        user_id: UUID,
        file_content: bytes,
        original_filename: str,
        content_type: str
    ) -> Document:
        """
        Upload file to Supabase Storage and process document.

        Args:
            user_id: ID of the user uploading the document
            file_content: File content as bytes
            original_filename: Original filename
            content_type: MIME content type

        Returns:
            Processed document
        """
        try:
            logger.info(f"Processing document upload for user {user_id}: {original_filename}")

            file_size = len(file_content)

            # Validate file
            self.validate_file(original_filename, file_size)

            # Upload to Supabase Storage
            file_path, storage_url, processed_filename = await self.storage_service.upload_file(
                user_id=user_id,
                file_content=file_content,
                original_filename=original_filename
            )

            # Create document record in database
            document = await self.supabase_service.create_document(
                user_id=user_id,
                filename=processed_filename,
                original_filename=original_filename,
                file_path=file_path,
                storage_url=storage_url,
                file_size=file_size,
                content_type=content_type
            )

            # Process document asynchronously
            try:
                # Download file for processing
                file_content_for_processing = await self.storage_service.download_file(file_path)

                # Create temporary file for processing
                temp_file_path = await self._create_temp_file(file_content_for_processing, original_filename)

                # Load and split document
                chunks = await self._load_and_split_document(temp_file_path, document.id)

                # Generate embeddings for chunks
                await self._generate_embeddings_for_chunks(chunks)

                # Update document status
                await self.supabase_service.update_document_status(
                    document.id,
                    "completed",
                    chunk_count=len(chunks)
                )

                # Clean up temporary file
                if temp_file_path.exists():
                    temp_file_path.unlink()

                logger.info(f"Successfully processed document: {original_filename} ({len(chunks)} chunks)")

                # Return updated document
                document.status = "completed"
                document.metadata.chunk_count = len(chunks)

                return document, chunks

            except Exception as e:
                # Update document status to failed
                await self.supabase_service.update_document_status(
                    document.id,
                    "failed",
                    error_message=str(e)
                )

                document.status = "failed"
                document.error_message = str(e)

                logger.error(f"Error processing document {original_filename}: {str(e)}")
                raise

        except Exception as e:
            logger.error(f"Error in upload_and_process_document: {str(e)}")
            raise

    async def _create_temp_file(self, file_content: bytes, filename: str) -> Path:
        """
        Create a temporary file for document processing.

        Args:
            file_content: File content as bytes
            filename: Original filename

        Returns:
            Path to temporary file
        """
        try:
            # Generate unique filename
            file_id = uuid4()
            file_extension = Path(filename).suffix
            unique_filename = f"{file_id}{file_extension}"
            temp_file_path = settings.upload_dir / unique_filename

            # Ensure upload directory exists
            settings.upload_dir.mkdir(parents=True, exist_ok=True)

            # Save file
            async with aiofiles.open(temp_file_path, 'wb') as f:
                await f.write(file_content)

            logger.info(f"Created temporary file: {temp_file_path}")
            return temp_file_path

        except Exception as e:
            logger.error(f"Error creating temporary file: {str(e)}")
            raise

    def validate_file(self, filename: str, file_size: int) -> None:
        """
        Validate uploaded file.

        Args:
            filename: Original filename
            file_size: File size in bytes

        Raises:
            ValueError: If file is invalid
        """
        # Check file size
        if file_size > settings.max_file_size:
            raise ValueError(f"File size ({file_size} bytes) exceeds maximum allowed size ({settings.max_file_size} bytes)")

        # Check file extension
        file_extension = Path(filename).suffix.lower()
        if file_extension not in settings.supported_file_types:
            raise ValueError(f"File type {file_extension} not supported. Supported types: {settings.supported_file_types}")

    async def _load_and_split_document(self, file_path: Path, document_id: UUID) -> List[DocumentChunk]:
        """
        Load document content and split into chunks.

        Args:
            file_path: Path to document file
            document_id: Document ID

        Returns:
            List of document chunks
        """
        try:
            file_extension = file_path.suffix.lower()

            # Load document based on file type
            if file_extension == ".pdf":
                loader = PyPDFLoader(str(file_path))
                documents = loader.load()
            elif file_extension in [".txt", ".md"]:
                loader = TextLoader(str(file_path), encoding="utf-8")
                documents = loader.load()
            else:
                raise ValueError(f"Unsupported file type: {file_extension}")

            # Split documents into chunks
            split_docs = self.text_splitter.split_documents(documents)

            # Create DocumentChunk objects
            chunks = []
            for i, doc in enumerate(split_docs):
                chunk = DocumentChunk(
                    id=f"{document_id}_{i}",
                    document_id=document_id,
                    chunk_index=i,
                    content=doc.page_content,
                    metadata={
                        "source": str(file_path),
                        "page": doc.metadata.get("page", 0),
                        **doc.metadata
                    }
                )
                chunks.append(chunk)

            logger.info(f"Split document into {len(chunks)} chunks")
            return chunks

        except Exception as e:
            logger.error(f"Error loading and splitting document: {str(e)}")
            raise

    async def _generate_embeddings_for_chunks(self, chunks: List[DocumentChunk]) -> None:
        """
        Generate embeddings for document chunks.

        Args:
            chunks: List of document chunks
        """
        try:
            logger.info(f"Generating embeddings for {len(chunks)} chunks")

            # Extract text content
            texts = [chunk.content for chunk in chunks]

            # Generate embeddings
            embeddings = await self.embedding_service.generate_embeddings(texts)

            # Assign embeddings to chunks
            for chunk, embedding in zip(chunks, embeddings):
                chunk.embedding = embedding

            logger.info(f"Successfully generated embeddings for {len(chunks)} chunks")

        except Exception as e:
            logger.error(f"Error generating embeddings for chunks: {str(e)}")
            raise

    async def get_document(self, document_id: UUID, user_id: UUID) -> Optional[Document]:
        """Get document by ID for a specific user."""
        return await self.supabase_service.get_document(document_id, user_id)

    async def list_user_documents(self, user_id: UUID, limit: int = 50, offset: int = 0) -> List[Document]:
        """List documents for a specific user."""
        return await self.supabase_service.list_user_documents(user_id, limit, offset)

    async def delete_document(self, document_id: UUID, user_id: UUID) -> bool:
        """
        Delete document and its file.

        Args:
            document_id: Document ID to delete
            user_id: User ID (for RLS)

        Returns:
            True if successful
        """
        try:
            # Get document to find file path
            document = await self.supabase_service.get_document(document_id, user_id)
            if not document:
                return False

            # Delete file from storage
            await self.storage_service.delete_file(document.file_path)

            # Delete document record
            success = await self.supabase_service.delete_document(document_id, user_id)

            if success:
                logger.info(f"Successfully deleted document: {document_id}")

            return success

        except Exception as e:
            logger.error(f"Error deleting document: {str(e)}")
            return False
