"""
Citation service using Anthropic's Citation API.
"""
import logging
from typing import List, <PERSON><PERSON>

import anthropic

from app.config import settings
from app.models import Citation, DocumentChunk, TextBlock

logger = logging.getLogger(__name__)


class CitationService:
    """Service for generating responses with citations using Anthropic API."""
    
    def __init__(self):
        self.client = anthropic.Anthropic(api_key=settings.anthropic_api_key)
        self.model = "claude-3-5-sonnet-20241022"  # Model that supports citations
    
    async def generate_cited_response(
        self,
        question: str,
        relevant_chunks: List[Tuple[DocumentChunk, float]],
        document_titles: dict[str, str]
    ) -> Tuple[str, List[TextBlock]]:
        """
        Generate a response with citations using Anthropic's Citation API.
        
        Args:
            question: User's question
            relevant_chunks: List of (chunk, similarity_score) tuples
            document_titles: Mapping of document_id to document title
            
        Returns:
            Tuple of (full_answer, content_blocks_with_citations)
        """
        try:
            logger.info(f"Generating cited response for question: {question[:100]}...")
            
            # Prepare documents for Anthropic Citation API
            documents = self._prepare_documents_for_citation(relevant_chunks, document_titles)
            
            # Create message content with documents and question
            message_content = []
            
            # Add documents with citations enabled
            for doc in documents:
                message_content.append({
                    "type": "document",
                    "source": {
                        "type": "text",
                        "media_type": "text/plain",
                        "data": doc["content"]
                    },
                    "title": doc["title"],
                    "context": doc["context"],
                    "citations": {"enabled": True}
                })
            
            # Add the question
            message_content.append({
                "type": "text",
                "text": f"Based on the provided documents, please answer the following question with proper citations: {question}"
            })
            
            # Make API call to Anthropic
            response = self.client.messages.create(
                model=self.model,
                max_tokens=2048,
                messages=[{
                    "role": "user",
                    "content": message_content
                }]
            )
            
            # Process response and extract citations
            content_blocks = self._process_anthropic_response(response, relevant_chunks, document_titles)
            
            # Generate full answer text
            full_answer = "".join([block.text for block in content_blocks])
            
            logger.info("Successfully generated cited response")
            return full_answer, content_blocks
            
        except Exception as e:
            logger.error(f"Error generating cited response: {str(e)}")
            raise
    
    def _prepare_documents_for_citation(
        self,
        relevant_chunks: List[Tuple[DocumentChunk, float]],
        document_titles: dict[str, str]
    ) -> List[dict]:
        """
        Prepare document chunks for Anthropic Citation API.
        
        Args:
            relevant_chunks: List of relevant document chunks
            document_titles: Mapping of document_id to title
            
        Returns:
            List of document dictionaries for API
        """
        documents = []
        
        for i, (chunk, score) in enumerate(relevant_chunks):
            doc_title = document_titles.get(str(chunk.document_id), f"Document {i+1}")
            
            # Create document for citation API
            doc = {
                "content": chunk.content,
                "title": doc_title,
                "context": f"Chunk {chunk.chunk_index} from {doc_title} (similarity: {score:.3f})"
            }
            documents.append(doc)
        
        return documents
    
    def _process_anthropic_response(
        self,
        response,
        relevant_chunks: List[Tuple[DocumentChunk, float]],
        document_titles: dict[str, str]
    ) -> List[TextBlock]:
        """
        Process Anthropic response and convert citations to our format.
        
        Args:
            response: Anthropic API response
            relevant_chunks: Original chunks for reference
            document_titles: Document title mapping
            
        Returns:
            List of TextBlock objects with citations
        """
        content_blocks = []
        
        try:
            # Process each content block from Anthropic response
            for block in response.content:
                if block.type == "text":
                    # Convert Anthropic citations to our format
                    citations = []
                    if hasattr(block, 'citations') and block.citations:
                        for citation in block.citations:
                            converted_citation = self._convert_anthropic_citation(
                                citation, relevant_chunks, document_titles
                            )
                            if converted_citation:
                                citations.append(converted_citation)
                    
                    # Create TextBlock
                    text_block = TextBlock(
                        type="text",
                        text=block.text,
                        citations=citations if citations else None
                    )
                    content_blocks.append(text_block)
            
            return content_blocks
            
        except Exception as e:
            logger.error(f"Error processing Anthropic response: {str(e)}")
            # Fallback: create a single text block without citations
            full_text = ""
            if hasattr(response, 'content') and response.content:
                full_text = "".join([block.text for block in response.content if hasattr(block, 'text')])
            
            return [TextBlock(type="text", text=full_text)]
    
    def _convert_anthropic_citation(
        self,
        anthropic_citation,
        relevant_chunks: List[Tuple[DocumentChunk, float]],
        document_titles: dict[str, str]
    ) -> Citation:
        """
        Convert Anthropic citation format to our Citation model.
        
        Args:
            anthropic_citation: Citation from Anthropic API
            relevant_chunks: Original chunks for reference
            document_titles: Document title mapping
            
        Returns:
            Citation object in our format
        """
        try:
            # Get document index and find corresponding chunk
            doc_index = anthropic_citation.document_index
            if doc_index < len(relevant_chunks):
                chunk, _ = relevant_chunks[doc_index]
                doc_title = document_titles.get(str(chunk.document_id), f"Document {doc_index + 1}")
            else:
                doc_title = f"Document {doc_index + 1}"
            
            # Create citation based on type
            citation = Citation(
                type=anthropic_citation.type,
                cited_text=anthropic_citation.cited_text,
                document_index=doc_index,
                document_title=doc_title
            )
            
            # Add type-specific fields
            if anthropic_citation.type == "char_location":
                citation.start_char_index = getattr(anthropic_citation, 'start_char_index', None)
                citation.end_char_index = getattr(anthropic_citation, 'end_char_index', None)
            elif anthropic_citation.type == "page_location":
                citation.start_page_number = getattr(anthropic_citation, 'start_page_number', None)
                citation.end_page_number = getattr(anthropic_citation, 'end_page_number', None)
            elif anthropic_citation.type == "content_block_location":
                citation.start_block_index = getattr(anthropic_citation, 'start_block_index', None)
                citation.end_block_index = getattr(anthropic_citation, 'end_block_index', None)
            
            return citation
            
        except Exception as e:
            logger.error(f"Error converting Anthropic citation: {str(e)}")
            return None
    
    async def generate_simple_response(
        self,
        question: str,
        context_chunks: List[DocumentChunk]
    ) -> str:
        """
        Generate a simple response without citations (fallback method).
        
        Args:
            question: User's question
            context_chunks: Relevant document chunks for context
            
        Returns:
            Generated response text
        """
        try:
            logger.info("Generating simple response without citations")
            
            # Prepare context from chunks
            context = "\n\n".join([
                f"Document chunk {i+1}:\n{chunk.content}"
                for i, chunk in enumerate(context_chunks)
            ])
            
            # Create prompt
            prompt = f"""Based on the following document excerpts, please answer the question.

Context:
{context}

Question: {question}

Answer:"""
            
            # Make API call
            response = self.client.messages.create(
                model=self.model,
                max_tokens=1024,
                messages=[{
                    "role": "user",
                    "content": prompt
                }]
            )
            
            # Extract text response
            if response.content and len(response.content) > 0:
                return response.content[0].text
            else:
                return "I couldn't generate a response based on the provided documents."
                
        except Exception as e:
            logger.error(f"Error generating simple response: {str(e)}")
            raise
