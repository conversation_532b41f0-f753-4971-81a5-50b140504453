"""
SQLAlchemy-based document service for better ORM capabilities.
"""
import logging
from datetime import datetime
from typing import List, Optional
from uuid import UUID

from sqlalchemy import and_, desc, func
from sqlalchemy.orm import Session

from app.database.connection import get_db_session
from app.database.models import Document as DocumentModel
from app.models import Document, DocumentMetadata

logger = logging.getLogger(__name__)


class DocumentServiceSQLAlchemy:
    """SQLAlchemy-based service for managing documents."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """
        Initialize the service.
        
        Args:
            db_session: Optional SQLAlchemy session. If not provided, 
                       a new session will be created for each operation.
        """
        self._db_session = db_session
    
    def _get_session(self) -> Session:
        """Get database session."""
        if self._db_session:
            return self._db_session
        return get_db_session()
    
    def _close_session(self, session: Session) -> None:
        """Close session if it was created by this service."""
        if not self._db_session:
            session.close()
    
    def create_document(
        self,
        user_id: UUID,
        filename: str,
        original_filename: str,
        file_path: str,
        storage_url: str,
        file_size: int,
        content_type: str
    ) -> Document:
        """
        Create a new document record.
        
        Args:
            user_id: ID of the user uploading the document
            filename: Processed filename
            original_filename: Original filename from upload
            file_path: Path in Supabase Storage
            storage_url: Public URL for the file
            file_size: Size of the file in bytes
            content_type: MIME type of the file
            
        Returns:
            Created document
        """
        session = self._get_session()
        try:
            logger.info(f"Creating document record for user {user_id}: {filename}")
            
            # Create SQLAlchemy model instance
            db_document = DocumentModel(
                user_id=user_id,
                filename=filename,
                original_filename=original_filename,
                file_path=file_path,
                storage_url=storage_url,
                file_size=file_size,
                content_type=content_type,
                status="processing"
            )
            
            session.add(db_document)
            session.commit()
            session.refresh(db_document)
            
            # Convert to Pydantic model
            document = self._convert_to_pydantic(db_document)
            
            logger.info(f"Successfully created document record: {document.id}")
            return document
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error creating document record: {str(e)}")
            raise
        finally:
            self._close_session(session)
    
    def get_document(self, document_id: UUID, user_id: UUID) -> Optional[Document]:
        """
        Get a document by ID for a specific user.
        
        Args:
            document_id: Document ID
            user_id: User ID (for access control)
            
        Returns:
            Document if found, None otherwise
        """
        session = self._get_session()
        try:
            db_document = session.query(DocumentModel).filter(
                and_(
                    DocumentModel.id == document_id,
                    DocumentModel.user_id == user_id
                )
            ).first()
            
            if not db_document:
                return None
            
            return self._convert_to_pydantic(db_document)
            
        except Exception as e:
            logger.error(f"Error getting document: {str(e)}")
            raise
        finally:
            self._close_session(session)
    
    def list_user_documents(
        self, 
        user_id: UUID, 
        limit: int = 50, 
        offset: int = 0,
        status_filter: Optional[str] = None
    ) -> List[Document]:
        """
        List documents for a specific user.
        
        Args:
            user_id: User ID
            limit: Maximum number of documents to return
            offset: Number of documents to skip
            status_filter: Optional status filter
            
        Returns:
            List of documents
        """
        session = self._get_session()
        try:
            query = session.query(DocumentModel).filter(
                DocumentModel.user_id == user_id
            )
            
            if status_filter:
                query = query.filter(DocumentModel.status == status_filter)
            
            db_documents = query.order_by(
                desc(DocumentModel.created_at)
            ).offset(offset).limit(limit).all()
            
            documents = [self._convert_to_pydantic(doc) for doc in db_documents]
            
            logger.info(f"Retrieved {len(documents)} documents for user {user_id}")
            return documents
            
        except Exception as e:
            logger.error(f"Error listing user documents: {str(e)}")
            raise
        finally:
            self._close_session(session)
    
    def update_document_status(
        self,
        document_id: UUID,
        status: str,
        error_message: Optional[str] = None,
        chunk_count: Optional[int] = None
    ) -> bool:
        """
        Update document processing status.
        
        Args:
            document_id: Document ID
            status: New status (processing, completed, failed)
            error_message: Error message if status is failed
            chunk_count: Number of chunks created
            
        Returns:
            True if successful
        """
        session = self._get_session()
        try:
            db_document = session.query(DocumentModel).filter(
                DocumentModel.id == document_id
            ).first()
            
            if not db_document:
                logger.warning(f"No document found with ID {document_id}")
                return False
            
            db_document.status = status
            if error_message is not None:
                db_document.error_message = error_message
            if chunk_count is not None:
                db_document.chunk_count = chunk_count
            
            session.commit()
            
            logger.info(f"Updated document {document_id} status to {status}")
            return True
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error updating document status: {str(e)}")
            raise
        finally:
            self._close_session(session)
    
    def delete_document(self, document_id: UUID, user_id: UUID) -> bool:
        """
        Delete a document record.
        
        Args:
            document_id: Document ID
            user_id: User ID (for access control)
            
        Returns:
            True if successful
        """
        session = self._get_session()
        try:
            result = session.query(DocumentModel).filter(
                and_(
                    DocumentModel.id == document_id,
                    DocumentModel.user_id == user_id
                )
            ).delete()
            
            session.commit()
            
            success = result > 0
            if success:
                logger.info(f"Deleted document record: {document_id}")
            else:
                logger.warning(f"No document found with ID {document_id} for user {user_id}")
            
            return success
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error deleting document: {str(e)}")
            raise
        finally:
            self._close_session(session)
    
    def get_user_document_count(self, user_id: UUID) -> int:
        """
        Get total document count for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            Total document count
        """
        session = self._get_session()
        try:
            count = session.query(func.count(DocumentModel.id)).filter(
                DocumentModel.user_id == user_id
            ).scalar()
            
            return count or 0
            
        except Exception as e:
            logger.error(f"Error getting document count: {str(e)}")
            raise
        finally:
            self._close_session(session)
    
    def _convert_to_pydantic(self, db_document: DocumentModel) -> Document:
        """
        Convert SQLAlchemy model to Pydantic model.
        
        Args:
            db_document: SQLAlchemy document model
            
        Returns:
            Pydantic document model
        """
        metadata = DocumentMetadata(
            filename=db_document.original_filename,
            file_size=db_document.file_size,
            content_type=db_document.content_type,
            upload_timestamp=db_document.created_at,
            chunk_count=db_document.chunk_count
        )
        
        return Document(
            id=db_document.id,
            user_id=db_document.user_id,
            filename=db_document.filename,
            original_filename=db_document.original_filename,
            file_path=db_document.file_path,
            storage_url=db_document.storage_url,
            metadata=metadata,
            status=db_document.status,
            error_message=db_document.error_message
        )
