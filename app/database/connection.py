"""
Database connection management for SQLAlchemy.
"""
import logging
from typing import Generator

from sqlalchemy import create_engine, Engine
from sqlalchemy.orm import sessionmaker, Session

from app.config import settings

logger = logging.getLogger(__name__)


def get_database_url() -> str:
    """
    Construct PostgreSQL database URL from Supabase configuration.

    For Supabase, you have several options:
    1. Use the DATABASE_URL environment variable (recommended)
    2. Use the connection string from Supabase dashboard
    3. Construct from Supabase URL (may not work for all projects)

    Returns:
        Database URL for SQLAlchemy
    """
    import os

    # Option 1: Use DATABASE_URL if provided
    database_url = os.getenv('DATABASE_URL')
    if database_url:
        return database_url

    # Option 2: Construct from Supabase settings
    # Extract project reference from Supabase URL
    project_id = settings.supabase_url.replace('https://', '').replace('http://', '').split('.')[0]

    # Try different connection formats
    # Format 1: Direct connection (most common for newer projects)
    db_host = f"db.{project_id}.supabase.co"
    database_url = f"postgresql://postgres:[YOUR_DB_PASSWORD]@{db_host}:5432/postgres"

    # Since we don't have the actual database password, we'll provide guidance
    logger.warning("Database connection requires manual configuration.")
    logger.warning("Please add DATABASE_URL to your .env file.")
    logger.warning("Get the connection string from: Supabase Dashboard > Settings > Database")
    logger.warning(f"Expected format: postgresql://postgres:[password]@{db_host}:5432/postgres")

    # For now, return a placeholder that will fail gracefully
    return f"postgresql://postgres:PLACEHOLDER_PASSWORD@{db_host}:5432/postgres"


def create_database_engine() -> Engine:
    """
    Create SQLAlchemy engine for database connections.

    Returns:
        SQLAlchemy Engine instance
    """
    database_url = get_database_url()

    # Create engine with connection pooling
    engine = create_engine(
        database_url,
        echo=False,  # Set to True for SQL query logging
        pool_size=5,
        max_overflow=10,
        pool_pre_ping=True,  # Verify connections before use
        pool_recycle=3600,   # Recycle connections every hour
    )

    logger.info("Database engine created successfully")
    return engine


# Global engine instance
engine = create_database_engine()

# Session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def get_db() -> Generator[Session, None, None]:
    """
    Dependency function to get database session.

    Yields:
        SQLAlchemy Session instance
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def get_db_session() -> Session:
    """
    Get a database session for direct use.

    Returns:
        SQLAlchemy Session instance
    """
    return SessionLocal()


def test_connection() -> bool:
    """
    Test database connection.

    Returns:
        True if connection successful, False otherwise
    """
    try:
        with engine.connect() as connection:
            result = connection.execute("SELECT 1")
            logger.info("Database connection test successful")
            return True
    except Exception as e:
        logger.error(f"Database connection test failed: {e}")
        return False
