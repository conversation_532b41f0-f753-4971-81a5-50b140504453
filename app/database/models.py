"""
SQLAlchemy ORM models for the RAG Citation API.
"""
import uuid
from datetime import datetime
from typing import Optional

from sqlalchemy import BigInteger, DateTime, Index, Integer, String, Text, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column

from app.database.base import Base, UUIDMixin


class Document(Base, UUIDMixin):
    """
    Document model for storing uploaded document metadata.

    This model represents documents uploaded by users with their metadata,
    storage information, and processing status. Each document is associated
    with a user through the user_id foreign key.

    Row Level Security (RLS) is enabled on this table in the database
    to ensure users can only access their own documents.
    """

    __tablename__ = "documents"

    # User association (references auth.users table in Supabase)
    user_id: Mapped[uuid.UUID] = mapped_column(
        UUID(as_uuid=True),
        nullable=False,
        comment="References auth.users(id) - the user who uploaded the document"
    )

    # File information
    filename: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Processed filename used for storage"
    )

    original_filename: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Original filename from upload"
    )

    file_path: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Path to file in Supabase Storage"
    )

    storage_url: Mapped[str] = mapped_column(
        Text,
        nullable=False,
        comment="Public URL for accessing the file"
    )

    # File metadata
    file_size: Mapped[int] = mapped_column(
        BigInteger,
        nullable=False,
        comment="File size in bytes"
    )

    content_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="MIME content type of the file"
    )

    # Processing information
    status: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="processing",
        server_default="processing",
        comment="Processing status: processing, completed, failed"
    )

    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Error message if processing failed"
    )

    chunk_count: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="Number of text chunks created from the document"
    )

    # Timestamp fields to match existing schema
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="Document upload timestamp"
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="Record last update timestamp"
    )

    # Indexes for performance
    __table_args__ = (
        # Index for user-specific document queries (most common query pattern)
        Index("idx_documents_user_id", "user_id"),

        # Index for sorting by upload timestamp
        Index("idx_documents_upload_timestamp", "created_at"),

        # Index for filtering by status
        Index("idx_documents_status", "status"),

        # Composite index for user + status queries
        Index("idx_documents_user_status", "user_id", "status"),

        # Composite index for user + timestamp (for pagination)
        Index("idx_documents_user_timestamp", "user_id", "created_at"),

        # Table comment
        {"comment": "Stores metadata for uploaded documents with user association"}
    )

    def __repr__(self) -> str:
        return f"<Document(id={self.id}, filename='{self.filename}', status='{self.status}')>"

    @property
    def is_processing(self) -> bool:
        """Check if document is currently being processed."""
        return self.status == "processing"

    @property
    def is_completed(self) -> bool:
        """Check if document processing is completed."""
        return self.status == "completed"

    @property
    def is_failed(self) -> bool:
        """Check if document processing failed."""
        return self.status == "failed"


# Note: The auth.users table is managed by Supabase Auth and is not defined here.
# It exists in the 'auth' schema and is referenced by the user_id foreign key.
#
# Row Level Security (RLS) policies are applied at the database level:
# - Users can only insert documents with their own user_id
# - Users can only select their own documents
# - Users can only update their own documents
# - Users can only delete their own documents
# - Service role can bypass RLS for admin operations
#
# These policies are defined in the migration files and applied directly
# to the database, not through SQLAlchemy.
