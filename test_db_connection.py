#!/usr/bin/env python3
"""
Test script to verify database connection and SQLAlchemy setup.
"""
from dotenv import load_dotenv
load_dotenv()

from app.database.connection import test_connection, get_database_url
from app.database.models import Document

def main():
    """Test database connection and setup."""
    print("🔍 Testing database connection and SQLAlchemy setup...")
    print("=" * 60)
    
    # Test database URL construction
    try:
        db_url = get_database_url()
        # Don't print the full URL as it contains credentials
        print(f"✓ Database URL constructed successfully")
        print(f"  Host: {db_url.split('@')[1].split(':')[0] if '@' in db_url else 'Unknown'}")
    except Exception as e:
        print(f"✗ Error constructing database URL: {e}")
        return False
    
    # Test database connection
    try:
        if test_connection():
            print("✓ Database connection successful")
        else:
            print("✗ Database connection failed")
            return False
    except Exception as e:
        print(f"✗ Database connection error: {e}")
        return False
    
    # Test SQLAlchemy model import
    try:
        print(f"✓ Document model imported successfully")
        print(f"  Table name: {Document.__tablename__}")
        print(f"  Columns: {list(Document.__table__.columns.keys())}")
    except Exception as e:
        print(f"✗ Error with SQLAlchemy models: {e}")
        return False
    
    print("\n✅ All database tests passed!")
    print("\nNext steps:")
    print("1. Create initial migration: alembic revision --autogenerate -m 'Initial migration'")
    print("2. Apply migration: alembic upgrade head")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)
