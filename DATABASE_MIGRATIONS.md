# Database Migrations with SQLAlchemy + Alembic

This project now uses SQLAlchemy ORM with Alembic for database migrations, providing a modern development workflow similar to JavaScript/Node.js frameworks like Drizzle ORM.

## Overview

- **SQLAlchemy ORM**: Type-safe database models with excellent IDE support
- **Alembic**: Automatic migration generation from model changes
- **Supabase Integration**: Works seamlessly with Supabase PostgreSQL
- **Modern Workflow**: Schema changes in code automatically generate migration scripts

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Database Connection

Add your Supabase database connection string to `.env`:

```bash
# Get this from Supabase Dashboard > Settings > Database > Connection string
DATABASE_URL=postgresql://postgres:[YOUR_PASSWORD]@db.[PROJECT_REF].supabase.co:5432/postgres
```

### 3. Run Setup

```bash
python setup.py
```

## Migration Commands

We provide a convenient `alembic_manager.py` script that wraps <PERSON><PERSON><PERSON> with a modern CLI:

### Generate Migration (Auto-detect Changes)

```bash
# Automatically detect model changes and generate migration
python alembic_manager.py generate "Add user preferences table"
```

### Apply Migrations

```bash
# Apply all pending migrations
python alembic_manager.py migrate

# Apply migrations up to specific revision
python alembic_manager.py migrate abc123
```

### Rollback Migrations

```bash
# Rollback one migration
python alembic_manager.py rollback -1

# Rollback to specific revision
python alembic_manager.py rollback abc123

# Rollback all migrations
python alembic_manager.py rollback base
```

### Check Migration Status

```bash
# Show current revision
python alembic_manager.py current

# Show migration history
python alembic_manager.py history

# Show migration status
python alembic_manager.py status
```

## Development Workflow

### 1. Modify Database Models

Edit your SQLAlchemy models in `app/database/models.py`:

```python
# Add a new field to the Document model
class Document(Base, UUIDMixin):
    # ... existing fields ...
    
    # New field
    tags: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="Document tags as JSON"
    )
```

### 2. Generate Migration

```bash
python alembic_manager.py generate "Add tags field to documents"
```

This automatically:
- Detects the schema changes
- Generates a migration file with timestamp
- Includes both upgrade and downgrade operations

### 3. Review Generated Migration

Check the generated file in `alembic/versions/`:

```python
def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('documents', sa.Column('tags', sa.Text(), nullable=True, comment='Document tags as JSON'))
    # ### end Alembic commands ###

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('documents', 'tags')
    # ### end Alembic commands ###
```

### 4. Apply Migration

```bash
python alembic_manager.py migrate
```

## Project Structure

```
app/
├── database/
│   ├── __init__.py
│   ├── base.py              # Base model classes and configuration
│   ├── connection.py        # Database connection management
│   └── models.py           # SQLAlchemy ORM models
├── services/
│   ├── document_service_sqlalchemy.py  # SQLAlchemy-based service
│   └── supabase_service.py            # Original Supabase service
alembic/
├── versions/               # Migration files
├── env.py                 # Alembic environment configuration
└── script.py.mako        # Migration template
alembic.ini                # Alembic configuration
alembic_manager.py         # Migration management CLI
```

## SQLAlchemy Models

### Document Model

```python
class Document(Base, UUIDMixin):
    """Document model with full type safety."""
    
    __tablename__ = "documents"
    
    user_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), nullable=False)
    filename: Mapped[str] = mapped_column(String(255), nullable=False)
    original_filename: Mapped[str] = mapped_column(String(255), nullable=False)
    file_path: Mapped[str] = mapped_column(Text, nullable=False)
    storage_url: Mapped[str] = mapped_column(Text, nullable=False)
    file_size: Mapped[int] = mapped_column(BigInteger, nullable=False)
    content_type: Mapped[str] = mapped_column(String(100), nullable=False)
    status: Mapped[str] = mapped_column(String(20), nullable=False, default="processing")
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    chunk_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
```

### Benefits

- **Type Safety**: Full type hints with IDE support
- **Validation**: Automatic validation of data types
- **Relationships**: Easy to define foreign keys and relationships
- **Queries**: Powerful query capabilities with type safety

## Services

### SQLAlchemy Service

Use the new SQLAlchemy-based service for better ORM capabilities:

```python
from app.services.document_service_sqlalchemy import DocumentServiceSQLAlchemy

# Create service
doc_service = DocumentServiceSQLAlchemy()

# Create document
document = doc_service.create_document(
    user_id=user_id,
    filename="example.pdf",
    original_filename="example.pdf",
    file_path="documents/user123/example.pdf",
    storage_url="https://...",
    file_size=1024,
    content_type="application/pdf"
)

# Query documents with filters
documents = doc_service.list_user_documents(
    user_id=user_id,
    status_filter="completed",
    limit=10
)
```

### Backward Compatibility

The original Supabase service remains available for backward compatibility:

```python
from app.services.supabase_service import SupabaseService
```

## Advanced Features

### Custom Migration

Create empty migration for custom SQL:

```bash
python alembic_manager.py generate "Add custom indexes" --manual
```

### Multiple Databases

The system supports multiple database configurations through environment variables.

### Testing

Use SQLAlchemy's testing utilities:

```python
from app.database.connection import get_db
from sqlalchemy.orm import Session

def test_document_creation(db: Session):
    # Test with real database session
    pass
```

## Troubleshooting

### Database Connection Issues

1. Verify `DATABASE_URL` in `.env`
2. Check Supabase dashboard for correct connection string
3. Ensure database password is correct

### Migration Conflicts

```bash
# Check current status
python alembic_manager.py status

# View history
python alembic_manager.py history

# Resolve conflicts manually in migration files
```

### Reset Migrations (Development Only)

```bash
# WARNING: This will lose all data
python alembic_manager.py rollback base
# Delete migration files
rm alembic/versions/*.py
# Recreate initial migration
python alembic_manager.py generate "Initial migration"
python alembic_manager.py migrate
```

## Best Practices

1. **Always review generated migrations** before applying
2. **Test migrations on development database** first
3. **Use descriptive migration messages**
4. **Keep migrations small and focused**
5. **Backup production database** before major migrations
6. **Use transactions** for complex migrations

## Comparison with JavaScript/Node.js

This setup provides a similar experience to modern JavaScript ORMs:

| Feature | This Project | Drizzle ORM | Prisma |
|---------|-------------|-------------|---------|
| Schema as Code | ✅ SQLAlchemy Models | ✅ | ✅ |
| Auto-generate Migrations | ✅ Alembic | ✅ | ✅ |
| Type Safety | ✅ Python Types | ✅ TypeScript | ✅ TypeScript |
| IDE Support | ✅ | ✅ | ✅ |
| Migration Rollback | ✅ | ✅ | ✅ |
