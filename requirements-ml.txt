# ML/AI dependencies - install after core dependencies
# These packages may have complex dependency trees

# AI/ML Libraries
anthropic>=0.8.1,<1.0.0
openai>=1.6.1,<2.0.0

# Document Processing
langchain>=0.1.0,<0.3.0
langchain-community>=0.0.10,<0.3.0
langchain-text-splitters>=0.0.1,<0.3.0
pypdf>=3.17.4,<5.0.0

# Vector Database
kdbai-client>=1.4.0,<2.0.0

# ML Libraries (install with specific constraints for Python 3.12)
sentence-transformers>=2.2.2,<3.0.0
numpy>=1.24.3,<2.0.0
pandas>=2.1.4,<3.0.0

# Optional: python-magic (may require system dependencies)
# python-magic>=0.4.27,<0.5.0
