# Database Schema Management Guide

This guide provides comprehensive instructions for managing database schemas in this SQLAlchemy + Alembic project with Supabase PostgreSQL.

## Table of Contents

1. [Schema Management](#schema-management)
2. [Migration Generation](#migration-generation)
3. [Migration Application](#migration-application)
4. [Troubleshooting](#troubleshooting)
5. [Best Practices](#best-practices)

## Schema Management

### File Structure and Locations

The database schema is defined using SQLAlchemy ORM models located in the following structure:

```
app/
├── database/
│   ├── __init__.py
│   ├── base.py              # Base model classes and mixins
│   ├── connection.py        # Database connection configuration
│   └── models.py           # Main model definitions
├── config.py               # Application configuration
└── main.py                # Application entry point

alembic/
├── versions/               # Migration files directory
├── env.py                 # Alembic environment configuration
└── script.py.mako         # Migration template

alembic.ini                # Alembic configuration file
```

### Model Definition Guidelines

#### 1. Base Model Location
- **File**: `app/database/base.py`
- Contains base classes and common mixins (e.g., `UUIDMixin`)

#### 2. Main Models Location
- **File**: `app/database/models.py`
- All application models should be defined here
- Import base classes from `app.database.base`

#### 3. Model Naming Conventions
- **Class names**: PascalCase (e.g., `Document`, `UserProfile`)
- **Table names**: snake_case (e.g., `documents`, `user_profiles`)
- **Column names**: snake_case (e.g., `user_id`, `created_at`)
- **Index names**: `idx_{table}_{column(s)}` (e.g., `idx_documents_user_id`)

#### 4. Example Model Structure

```python
from sqlalchemy import String, Text, DateTime, func
from sqlalchemy.orm import Mapped, mapped_column
from app.database.base import Base, UUIDMixin

class Document(Base, UUIDMixin):
    """Document model with proper documentation."""
    
    __tablename__ = "documents"
    
    # Column definitions with type hints
    filename: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="Descriptive comment"
    )
    
    # Indexes and constraints
    __table_args__ = (
        Index("idx_documents_filename", "filename"),
        {"comment": "Table description"}
    )
```

### Adding New Models

1. **Define the model** in `app/database/models.py`
2. **Import necessary types** from SQLAlchemy
3. **Add proper type hints** using `Mapped[Type]`
4. **Include table comments** for documentation
5. **Define indexes** for performance optimization
6. **Add relationships** if needed

### Modifying Existing Models

1. **Update the model definition** in `app/database/models.py`
2. **Test changes locally** before generating migrations
3. **Generate migration** using Alembic
4. **Review generated migration** before applying
5. **Apply migration** to database

## Migration Generation

### Prerequisites

Ensure your environment is properly configured:

```bash
# Activate virtual environment
source venv/bin/activate  # or your environment activation command

# Verify database connection
python test_db_connection.py

# Check current migration status
alembic current
```

### Generating Migrations

#### 1. Auto-generate Migration

```bash
# Generate migration with descriptive name
alembic revision --autogenerate -m "Add user_profile table"

# For more specific changes
alembic revision --autogenerate -m "Add index to documents.status column"
alembic revision --autogenerate -m "Modify user_id column to be non-nullable"
```

#### 2. Manual Migration (for complex changes)

```bash
# Create empty migration file
alembic revision -m "Custom data migration for user preferences"
```

#### 3. Migration Naming Best Practices

- Use descriptive, action-oriented names
- Include the affected table/column when relevant
- Examples:
  - `"Add documents table with RLS policies"`
  - `"Create indexes for document search performance"`
  - `"Modify user_profile.avatar_url to allow null"`
  - `"Add cascade delete to document_chunks"`

### Migration File Structure

Generated migrations follow this naming pattern:
```
YYYYMMDD_HHMM_{revision_id}_{slug}.py
```

Example: `20241225_1430_abc123def456_add_documents_table.py`

### Reviewing Generated Migrations

Before applying migrations, always review the generated file:

```bash
# View the latest migration
ls -la alembic/versions/ | tail -1

# Open and review the migration file
cat alembic/versions/20241225_1430_abc123def456_add_documents_table.py
```

#### Key Review Points:

1. **Check upgrade() function** - Ensure it creates intended changes
2. **Check downgrade() function** - Verify rollback logic is correct
3. **Verify data types** - Ensure PostgreSQL-compatible types
4. **Review indexes** - Confirm performance optimizations
5. **Check constraints** - Validate foreign keys and constraints

## Migration Application

### Checking Migration Status

```bash
# Show current migration version
alembic current

# Show migration history
alembic history

# Show pending migrations
alembic show head
```

### Applying Migrations

#### 1. Apply All Pending Migrations

```bash
# Apply all pending migrations to latest
alembic upgrade head

# Apply with verbose output
alembic upgrade head --sql

# Dry run (show SQL without executing)
alembic upgrade head --sql > migration_preview.sql
```

#### 2. Apply Specific Migration

```bash
# Upgrade to specific revision
alembic upgrade abc123def456

# Upgrade one step at a time
alembic upgrade +1
```

#### 3. Rollback Migrations

```bash
# Rollback one migration
alembic downgrade -1

# Rollback to specific revision
alembic downgrade abc123def456

# Rollback to base (WARNING: This drops all tables)
alembic downgrade base
```

### Post-Migration Verification

After applying migrations:

1. **Test database connection**:
   ```bash
   python test_db_connection.py
   ```

2. **Verify schema changes**:
   ```sql
   -- Connect to database and verify
   \d documents  -- Describe table structure
   \di           -- List indexes
   ```

3. **Run application tests**:
   ```bash
   python -m pytest tests/
   ```

## Troubleshooting

### Common Issues and Solutions

#### 1. Migration Generation Issues

**Problem**: Alembic doesn't detect model changes
```bash
# Solution: Ensure models are properly imported
# Check alembic/env.py imports all models
```

**Problem**: "Target database is not up to date"
```bash
# Solution: Check and resolve migration conflicts
alembic current
alembic history
alembic upgrade head
```

#### 2. Migration Application Issues

**Problem**: Foreign key constraint errors
```bash
# Solution: Check migration order and dependencies
# Ensure parent tables are created before child tables
```

**Problem**: Column already exists errors
```bash
# Solution: Check for duplicate migrations
alembic history
# Remove duplicate migration files if found
```

#### 3. Database Connection Issues

**Problem**: Connection timeout or hostname resolution
```bash
# Solution: Verify DATABASE_URL in .env
echo $DATABASE_URL

# Test connection
python test_db_connection.py

# Use connection pooler instead of direct connection
# Format: postgresql://postgres.[PROJECT-REF]:[PASSWORD]@aws-0-[REGION].pooler.supabase.com:5432/postgres
```

#### 4. Supabase-Specific Issues

**Problem**: RLS (Row Level Security) conflicts
```sql
-- Temporarily disable RLS for migration
ALTER TABLE documents DISABLE ROW LEVEL SECURITY;
-- Run migration
-- Re-enable RLS
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
```

**Problem**: Auth schema conflicts
```bash
# Ensure migrations don't modify auth schema
# Auth tables are managed by Supabase
```

### Recovery Procedures

#### 1. Rollback Failed Migration

```bash
# Check current state
alembic current

# Rollback to previous working state
alembic downgrade -1

# Fix migration file
# Re-apply corrected migration
alembic upgrade head
```

#### 2. Reset Migration History (DANGEROUS)

```bash
# Only use in development!
# This will lose all migration history

# Drop all tables
alembic downgrade base

# Remove migration files
rm alembic/versions/*.py

# Recreate initial migration
alembic revision --autogenerate -m "Initial migration"

# Apply migration
alembic upgrade head
```

## Best Practices

### 1. Development Workflow

1. **Always work in feature branches**
2. **Test schema changes locally first**
3. **Generate migrations in development environment**
4. **Review migrations before committing**
5. **Test migrations on staging before production**

### 2. Migration Safety

#### Before Generating Migrations:
- [ ] Backup database (if production)
- [ ] Test model changes locally
- [ ] Ensure all team members have applied latest migrations
- [ ] Check for conflicting changes from other developers

#### Before Applying Migrations:
- [ ] Review generated migration file
- [ ] Test migration on development database
- [ ] Verify rollback procedure works
- [ ] Ensure application code is compatible with schema changes

#### After Applying Migrations:
- [ ] Verify schema changes are correct
- [ ] Test application functionality
- [ ] Monitor for performance issues
- [ ] Update documentation if needed

### 3. Performance Considerations

#### Index Management:
```python
# Add indexes for frequently queried columns
__table_args__ = (
    Index("idx_documents_user_id", "user_id"),           # Foreign key
    Index("idx_documents_status", "status"),             # Filter column
    Index("idx_documents_created_at", "created_at"),     # Sort column
    Index("idx_documents_user_status", "user_id", "status"),  # Composite
)
```

#### Large Table Modifications:
- Use `CONCURRENTLY` for index creation on large tables
- Consider batched updates for data migrations
- Plan maintenance windows for schema changes

### 4. Team Collaboration

#### Migration Conflicts:
- Communicate schema changes with team
- Use descriptive migration names
- Merge migrations in order
- Resolve conflicts by creating merge migrations

#### Code Reviews:
- Always review migration files in pull requests
- Verify backward compatibility
- Check for data loss scenarios
- Ensure proper rollback procedures

### 5. Production Deployment

#### Pre-deployment Checklist:
- [ ] Test migrations on staging environment
- [ ] Verify application compatibility
- [ ] Plan rollback strategy
- [ ] Schedule maintenance window if needed
- [ ] Backup production database

#### Deployment Process:
1. **Backup database**
2. **Apply migrations**: `alembic upgrade head`
3. **Verify schema changes**
4. **Deploy application code**
5. **Monitor for issues**
6. **Rollback if necessary**

### 6. Monitoring and Maintenance

#### Regular Tasks:
- Monitor migration performance
- Clean up old migration files (keep history)
- Update documentation
- Review and optimize indexes
- Monitor database performance metrics

#### Alerting:
- Set up alerts for migration failures
- Monitor database connection issues
- Track schema change impacts on performance

---

## Quick Reference Commands

```bash
# Check status
alembic current
alembic history

# Generate migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback
alembic downgrade -1

# Test connection
python test_db_connection.py

# Preview SQL
alembic upgrade head --sql
```

For more detailed information, refer to the [Alembic documentation](https://alembic.sqlalchemy.org/) and [SQLAlchemy documentation](https://docs.sqlalchemy.org/).
