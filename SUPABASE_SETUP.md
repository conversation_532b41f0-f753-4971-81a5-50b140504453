# Supabase Integration Setup Guide

This guide explains how to set up the RAG Citation API with <PERSON>pa<PERSON> for document storage and user authentication.

## Overview

The integration includes:
- **Supabase Storage**: For secure file storage with user isolation
- **Supabase PostgreSQL**: For document metadata with Row Level Security (RLS)
- **Supabase Auth**: For user authentication and authorization

## Prerequisites

1. A Supabase project (create one at [supabase.com](https://supabase.com))
2. Python 3.8+ environment
3. Required API keys (Anthropic, OpenAI, KDB.AI)

## Supabase Project Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Note down your project URL and API keys from the project settings

### 2. Configure Environment Variables

Copy `.env.example` to `.env` and fill in your Supabase credentials:

```bash
# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
SUPABASE_STORAGE_BUCKET=documents
```

### 3. Run Setup Script

The setup script will install dependencies, run migrations, and configure storage:

```bash
python setup.py
```

### 4. Manual Supabase Configuration (if needed)

If the setup script fails, you can manually configure Supabase:

#### Enable Authentication
1. Go to Authentication > Settings in your Supabase dashboard
2. Configure your authentication providers (email, OAuth, etc.)

#### Create Storage Bucket
1. Go to Storage in your Supabase dashboard
2. Create a new bucket named `documents`
3. Set it as private (not public)

#### Run Database Migrations
```bash
python migrate.py
```

## Database Schema

The integration creates the following database structure:

### Documents Table
```sql
CREATE TABLE documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_path TEXT NOT NULL,
    storage_url TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    content_type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'processing',
    error_message TEXT,
    chunk_count INTEGER,
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### Row Level Security (RLS)

RLS policies ensure users can only access their own documents:

- **Insert**: Users can only create documents with their own user_id
- **Select**: Users can only view their own documents
- **Update**: Users can only update their own documents
- **Delete**: Users can only delete their own documents

## API Authentication

All document endpoints now require authentication:

### Headers Required
```
Authorization: Bearer <jwt_token>
```

### Getting JWT Token
The JWT token should be obtained from your client-side Supabase Auth implementation.

## API Endpoints

### Upload Document
```http
POST /api/v1/documents/upload
Authorization: Bearer <jwt_token>
Content-Type: multipart/form-data

file: <file_data>
title: <optional_title>
```

### List User Documents
```http
GET /api/v1/documents?limit=50&offset=0
Authorization: Bearer <jwt_token>
```

### Get Document
```http
GET /api/v1/documents/{document_id}
Authorization: Bearer <jwt_token>
```

### Delete Document
```http
DELETE /api/v1/documents/{document_id}
Authorization: Bearer <jwt_token>
```

### Query Documents
```http
POST /api/v1/query
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "question": "Your question here",
  "document_ids": ["optional", "document", "ids"],
  "max_results": 5,
  "include_citations": true
}
```

## File Storage Structure

Files are stored in Supabase Storage with the following structure:
```
documents/
├── {user_id_1}/
│   ├── {document_id_1}.pdf
│   └── {document_id_2}.txt
├── {user_id_2}/
│   ├── {document_id_3}.md
│   └── {document_id_4}.pdf
```

This ensures complete user isolation at the storage level.

## Security Features

1. **Row Level Security**: Database-level access control
2. **User Isolation**: Files stored in user-specific folders
3. **JWT Verification**: All requests verified against Supabase Auth
4. **Private Storage**: Files not publicly accessible
5. **Signed URLs**: Temporary access URLs for file downloads

## Troubleshooting

### Migration Issues
```bash
# Check migration status
python migrate.py --status

# Re-run migrations
python migrate.py
```

### Storage Issues
- Verify bucket exists in Supabase dashboard
- Check service role key permissions
- Ensure bucket is set to private

### Authentication Issues
- Verify JWT token is valid
- Check token expiration
- Ensure user exists in auth.users table

## Development

### Running Locally
```bash
# Install dependencies
pip install -r requirements.txt

# Run setup
python setup.py

# Start server
python run.py
```

### Testing
```bash
# Run tests
pytest

# Test with authentication
curl -H "Authorization: Bearer <jwt_token>" \
     http://localhost:8000/api/v1/documents
```
