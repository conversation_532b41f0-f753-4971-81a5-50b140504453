"""Initial migration

Revision ID: 4ca3f0f7efac
Revises: 
Create Date: 2025-05-25 17:38:17.795614

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '4ca3f0f7efac'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('documents',
    sa.Column('user_id', sa.UUID(), nullable=False, comment='References auth.users(id) - the user who uploaded the document'),
    sa.Column('filename', sa.String(length=255), nullable=False, comment='Processed filename used for storage'),
    sa.Column('original_filename', sa.String(length=255), nullable=False, comment='Original filename from upload'),
    sa.Column('file_path', sa.Text(), nullable=False, comment='Path to file in Supabase Storage'),
    sa.Column('storage_url', sa.Text(), nullable=False, comment='Public URL for accessing the file'),
    sa.Column('file_size', sa.BigInteger(), nullable=False, comment='File size in bytes'),
    sa.Column('content_type', sa.String(length=100), nullable=False, comment='MIME content type of the file'),
    sa.Column('status', sa.String(length=20), server_default='processing', nullable=False, comment='Processing status: processing, completed, failed'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='Error message if processing failed'),
    sa.Column('chunk_count', sa.Integer(), nullable=True, comment='Number of text chunks created from the document'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Document upload timestamp'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='Record last update timestamp'),
    sa.Column('id', sa.UUID(), server_default=sa.text('gen_random_uuid()'), nullable=False, comment='Primary key UUID'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_documents')),
    comment='Stores metadata for uploaded documents with user association'
    )
    op.create_index('idx_documents_status', 'documents', ['status'], unique=False)
    op.create_index('idx_documents_upload_timestamp', 'documents', ['created_at'], unique=False)
    op.create_index('idx_documents_user_id', 'documents', ['user_id'], unique=False)
    op.create_index('idx_documents_user_status', 'documents', ['user_id', 'status'], unique=False)
    op.create_index('idx_documents_user_timestamp', 'documents', ['user_id', 'created_at'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_documents_user_timestamp', table_name='documents')
    op.drop_index('idx_documents_user_status', table_name='documents')
    op.drop_index('idx_documents_user_id', table_name='documents')
    op.drop_index('idx_documents_upload_timestamp', table_name='documents')
    op.drop_index('idx_documents_status', table_name='documents')
    op.drop_table('documents')
    # ### end Alembic commands ###
