#!/usr/bin/env python3
"""
Test script to verify environment variable loading.
"""
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

from app.config import settings

def test_environment():
    """Test if environment variables are loaded correctly."""
    print("🔍 Testing environment variable loading...")
    print("=" * 50)
    
    # Check .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print(f"✓ .env file found: {env_file.absolute()}")
    else:
        print(f"✗ .env file not found: {env_file.absolute()}")
        return False
    
    # Test Supabase settings
    try:
        print(f"✓ SUPABASE_URL: {settings.supabase_url}")
        print(f"✓ SUPABASE_ANON_KEY: {settings.supabase_anon_key[:20]}...")
        print(f"✓ SUPABASE_SERVICE_ROLE_KEY: {settings.supabase_service_role_key[:20]}...")
        print(f"✓ SUPABASE_STORAGE_BUCKET: {settings.supabase_storage_bucket}")
    except Exception as e:
        print(f"✗ Error loading Supabase settings: {e}")
        return False
    
    # Test other API keys
    try:
        print(f"✓ ANTHROPIC_API_KEY: {settings.anthropic_api_key[:20]}...")
        print(f"✓ OPENAI_API_KEY: {settings.openai_api_key[:20]}...")
        print(f"✓ KDBAI_ENDPOINT: {settings.kdbai_endpoint}")
        print(f"✓ KDBAI_API_KEY: {settings.kdbai_api_key[:20]}...")
    except Exception as e:
        print(f"✗ Error loading API keys: {e}")
        return False
    
    print("\n✅ All environment variables loaded successfully!")
    return True

if __name__ == "__main__":
    success = test_environment()
    if not success:
        exit(1)
