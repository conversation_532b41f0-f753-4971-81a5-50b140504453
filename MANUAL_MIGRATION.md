# Manual Database Migration Guide

If the automated migration script fails, you can run the migrations manually using the Supabase SQL Editor.

## Steps

1. **Open Supabase Dashboard**
   - Go to [supabase.com](https://supabase.com)
   - Navigate to your project
   - Click on "SQL Editor" in the left sidebar

2. **Run Migration Files in Order**
   
   Execute the following SQL scripts in order:

### Migration 1: Create Documents Table

```sql
-- Create documents table with user association
-- This migration creates the main documents table that stores metadata about uploaded files

CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_path TEXT NOT NULL, -- Supabase Storage path
    storage_url TEXT NOT NULL, -- Public URL from Supabase Storage
    file_size BIGINT NOT NULL,
    content_type TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'processing', -- processing, completed, failed
    error_message TEXT,
    chunk_count INTEGER,
    upload_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_documents_updated_at 
    BEFORE UPDATE ON documents 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE documents IS 'Stores metadata for uploaded documents with user association';
COMMENT ON COLUMN documents.user_id IS 'References auth.users(id) - the user who uploaded the document';
COMMENT ON COLUMN documents.file_path IS 'Path to file in Supabase Storage';
COMMENT ON COLUMN documents.storage_url IS 'Public URL for accessing the file';
COMMENT ON COLUMN documents.status IS 'Processing status: processing, completed, failed';
```

### Migration 2: Create Indexes

```sql
-- Create performance indexes for the documents table
-- This migration adds indexes to improve query performance

-- Index for user-specific document queries (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_documents_user_id 
ON documents(user_id);

-- Index for sorting by upload timestamp
CREATE INDEX IF NOT EXISTS idx_documents_upload_timestamp 
ON documents(upload_timestamp DESC);

-- Index for filtering by status
CREATE INDEX IF NOT EXISTS idx_documents_status 
ON documents(status);

-- Composite index for user + status queries
CREATE INDEX IF NOT EXISTS idx_documents_user_status 
ON documents(user_id, status);

-- Composite index for user + timestamp (for pagination)
CREATE INDEX IF NOT EXISTS idx_documents_user_timestamp 
ON documents(user_id, upload_timestamp DESC);

-- Add comments for documentation
COMMENT ON INDEX idx_documents_user_id IS 'Improves performance for user-specific document queries';
COMMENT ON INDEX idx_documents_upload_timestamp IS 'Improves performance for sorting by upload time';
COMMENT ON INDEX idx_documents_status IS 'Improves performance for filtering by processing status';
COMMENT ON INDEX idx_documents_user_status IS 'Improves performance for user + status filter queries';
COMMENT ON INDEX idx_documents_user_timestamp IS 'Improves performance for user document pagination';
```

### Migration 3: Create RLS Policies

```sql
-- Create Row Level Security (RLS) policies for the documents table
-- This migration ensures users can only access their own documents

-- Enable RLS on the documents table
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only insert their own documents
CREATE POLICY "Users can insert their own documents" ON documents
    FOR INSERT 
    WITH CHECK (auth.uid() = user_id);

-- Policy: Users can only view their own documents
CREATE POLICY "Users can view their own documents" ON documents
    FOR SELECT 
    USING (auth.uid() = user_id);

-- Policy: Users can only update their own documents
CREATE POLICY "Users can update their own documents" ON documents
    FOR UPDATE 
    USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- Policy: Users can only delete their own documents
CREATE POLICY "Users can delete their own documents" ON documents
    FOR DELETE 
    USING (auth.uid() = user_id);

-- Create a policy for service role to bypass RLS (for admin operations)
CREATE POLICY "Service role can manage all documents" ON documents
    FOR ALL 
    USING (current_setting('request.jwt.claims', true)::json->>'role' = 'service_role')
    WITH CHECK (current_setting('request.jwt.claims', true)::json->>'role' = 'service_role');

-- Add comments for documentation
COMMENT ON POLICY "Users can insert their own documents" ON documents IS 'Allows users to upload documents associated with their user ID';
COMMENT ON POLICY "Users can view their own documents" ON documents IS 'Restricts document viewing to the owner only';
COMMENT ON POLICY "Users can update their own documents" ON documents IS 'Allows users to update only their own documents';
COMMENT ON POLICY "Users can delete their own documents" ON documents IS 'Allows users to delete only their own documents';
COMMENT ON POLICY "Service role can manage all documents" ON documents IS 'Allows service role to bypass RLS for admin operations';
```

## Verification

After running all migrations, verify they were applied correctly:

```sql
-- Check if documents table exists
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'documents';

-- Check if indexes were created
SELECT indexname 
FROM pg_indexes 
WHERE tablename = 'documents';

-- Check if RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename = 'documents';

-- Check RLS policies
SELECT policyname, cmd, qual 
FROM pg_policies 
WHERE tablename = 'documents';
```

## Next Steps

After completing the manual migrations:

1. **Create Storage Bucket**
   - Go to Storage in your Supabase dashboard
   - Create a new bucket named `documents`
   - Set it as private (not public)

2. **Test the Setup**
   ```bash
   python test_env.py
   python setup.py
   ```

3. **Start the API**
   ```bash
   python run.py
   ```
