# RAG Citation API Documentation

## Overview

The RAG Citation API is a REST API server for Retrieval-Augmented Generation with integrated citation support using Anthropic's Citation API. This system processes academic documents, stores them in a vector database, and provides question-answering capabilities with proper source citations.

**Base URL:** `http://localhost:8000/api/v1`

**API Version:** 1.0.0

## Authentication

Currently, no authentication is required. All endpoints are publicly accessible.

## Content Types

- **Request Content-Type:** `application/json` (except file uploads)
- **Response Content-Type:** `application/json`
- **File Upload Content-Type:** `multipart/form-data`

## Common Headers

### Standard Requests
```http
Content-Type: application/json
Accept: application/json
```

### File Upload Requests
```http
Content-Type: multipart/form-data
Accept: application/json
```

## Endpoints

### 1. Health Check

Check the health status of the API and its dependencies.

**Endpoint:** `GET /health`

**Headers:**
```http
Accept: application/json
```

**Request:**
```http
GET /api/v1/health HTTP/1.1
Host: localhost:8000
Accept: application/json
```

**Response (200 OK):**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00.123456Z",
  "version": "1.0.0",
  "services": {
    "vector_database": "healthy",
    "anthropic_api": "healthy",
    "openai_api": "healthy"
  }
}
```

**Response (503 Service Unavailable):**
```json
{
  "error": "Service unhealthy",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Status Codes:**
- `200` - Service is healthy
- `503` - Service unavailable

---

### 2. Upload Document

Upload and process a document for RAG operations.

**Endpoint:** `POST /documents/upload`

**Headers:**
```http
Content-Type: multipart/form-data
Accept: application/json
```

**Request Parameters:**
- `file` (required): Document file (PDF, TXT, MD)
- `title` (optional): Custom title for the document

**Request:**
```http
POST /api/v1/documents/upload HTTP/1.1
Host: localhost:8000
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Accept: application/json

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="research_paper.pdf"
Content-Type: application/pdf

[PDF binary data]
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="title"

AI Research Paper
------WebKitFormBoundary7MA4YWxkTrZu0gW--
```

**Response (200 OK):**
```json
{
  "document": {
    "id": "550e8400-e29b-41d4-a716-************",
    "filename": "AI Research Paper",
    "file_path": "/uploads/550e8400-e29b-41d4-a716-************.pdf",
    "metadata": {
      "filename": "research_paper.pdf",
      "file_size": 2048576,
      "content_type": "application/pdf",
      "upload_timestamp": "2024-01-15T10:30:00.123456Z",
      "chunk_count": 25
    },
    "status": "completed",
    "error_message": null
  },
  "message": "Document uploaded and processed successfully. Created 25 chunks."
}
```

**Response (400 Bad Request):**
```json
{
  "error": "File size (52428800 bytes) exceeds maximum allowed size (50000000 bytes)",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Response (500 Internal Server Error):**
```json
{
  "error": "Failed to upload and process document",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Status Codes:**
- `200` - Document uploaded and processed successfully
- `400` - Invalid file or validation error
- `413` - File too large
- `500` - Server error during processing

**File Specifications:**
- **Supported formats:** PDF (.pdf), Text (.txt), Markdown (.md)
- **Maximum file size:** 50MB (50,000,000 bytes)
- **Encoding:** UTF-8 for text files

---

### 3. List Documents

Retrieve a list of all uploaded documents.

**Endpoint:** `GET /documents`

**Headers:**
```http
Accept: application/json
```

**Request:**
```http
GET /api/v1/documents HTTP/1.1
Host: localhost:8000
Accept: application/json
```

**Response (200 OK):**
```json
{
  "documents": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "filename": "AI Research Paper",
      "file_path": "/uploads/550e8400-e29b-41d4-a716-************.pdf",
      "metadata": {
        "filename": "research_paper.pdf",
        "file_size": 2048576,
        "content_type": "application/pdf",
        "upload_timestamp": "2024-01-15T10:30:00.123456Z",
        "chunk_count": 25
      },
      "status": "completed",
      "error_message": null
    },
    {
      "id": "660f9511-f3ac-52e5-b827-557766551111",
      "filename": "Technical Documentation",
      "file_path": "/uploads/660f9511-f3ac-52e5-b827-557766551111.txt",
      "metadata": {
        "filename": "tech_docs.txt",
        "file_size": 1024000,
        "content_type": "text/plain",
        "upload_timestamp": "2024-01-15T11:15:00.123456Z",
        "chunk_count": 15
      },
      "status": "completed",
      "error_message": null
    }
  ],
  "total_count": 2
}
```

**Response (500 Internal Server Error):**
```json
{
  "error": "Failed to list documents",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Status Codes:**
- `200` - Success
- `500` - Server error

---

### 4. Get Document

Retrieve details of a specific document by ID.

**Endpoint:** `GET /documents/{document_id}`

**Headers:**
```http
Accept: application/json
```

**Path Parameters:**
- `document_id` (required): UUID of the document

**Request:**
```http
GET /api/v1/documents/550e8400-e29b-41d4-a716-************ HTTP/1.1
Host: localhost:8000
Accept: application/json
```

**Response (200 OK):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "filename": "AI Research Paper",
  "file_path": "/uploads/550e8400-e29b-41d4-a716-************.pdf",
  "metadata": {
    "filename": "research_paper.pdf",
    "file_size": 2048576,
    "content_type": "application/pdf",
    "upload_timestamp": "2024-01-15T10:30:00.123456Z",
    "chunk_count": 25
  },
  "status": "completed",
  "error_message": null
}
```

**Response (404 Not Found):**
```json
{
  "error": "Document not found",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Response (500 Internal Server Error):**
```json
{
  "error": "Failed to get document",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Status Codes:**
- `200` - Document found
- `404` - Document not found
- `500` - Server error

---

### 5. Delete Document

Delete a document and all its associated chunks from the system.

**Endpoint:** `DELETE /documents/{document_id}`

**Headers:**
```http
Accept: application/json
```

**Path Parameters:**
- `document_id` (required): UUID of the document to delete

**Request:**
```http
DELETE /api/v1/documents/550e8400-e29b-41d4-a716-************ HTTP/1.1
Host: localhost:8000
Accept: application/json
```

**Response (200 OK):**
```json
{
  "message": "Document 550e8400-e29b-41d4-a716-************ deleted successfully"
}
```

**Response (404 Not Found):**
```json
{
  "error": "Document not found",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Response (500 Internal Server Error):**
```json
{
  "error": "Failed to delete document",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Status Codes:**
- `200` - Document deleted successfully
- `404` - Document not found
- `500` - Server error

---

### 6. Query Documents

Ask questions about uploaded documents and receive answers with citations.

**Endpoint:** `POST /query`

**Headers:**
```http
Content-Type: application/json
Accept: application/json
```

**Request Body Parameters:**
- `question` (required): The question to ask (string, 1-1000 characters)
- `document_ids` (optional): Array of document UUIDs to search in
- `max_results` (optional): Maximum number of results (integer, 1-20, default: 5)
- `include_citations` (optional): Whether to include citations (boolean, default: true)

**Request:**
```http
POST /api/v1/query HTTP/1.1
Host: localhost:8000
Content-Type: application/json
Accept: application/json

{
  "question": "What are the main contributions of this research?",
  "document_ids": ["550e8400-e29b-41d4-a716-************"],
  "max_results": 5,
  "include_citations": true
}
```

**Response (200 OK) - With Citations:**
```json
{
  "answer": "The main contributions of this research include the development of a novel neural architecture and improved performance metrics.",
  "content_blocks": [
    {
      "type": "text",
      "text": "The main contributions include ",
      "citations": null
    },
    {
      "type": "text",
      "text": "a novel neural architecture that achieves state-of-the-art performance",
      "citations": [
        {
          "type": "char_location",
          "cited_text": "We propose a novel neural architecture that achieves state-of-the-art performance on benchmark datasets.",
          "document_index": 0,
          "document_title": "AI Research Paper",
          "start_char_index": 1250,
          "end_char_index": 1350,
          "start_page_number": null,
          "end_page_number": null,
          "start_block_index": null,
          "end_block_index": null
        }
      ]
    },
    {
      "type": "text",
      "text": " and improved metrics.",
      "citations": null
    }
  ],
  "sources": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "filename": "AI Research Paper",
      "file_path": "/uploads/550e8400-e29b-41d4-a716-************.pdf",
      "metadata": {
        "filename": "research_paper.pdf",
        "file_size": 2048576,
        "content_type": "application/pdf",
        "upload_timestamp": "2024-01-15T10:30:00.123456Z",
        "chunk_count": 25
      },
      "status": "completed",
      "error_message": null
    }
  ],
  "processing_time": 2.45
}
```

**Response (200 OK) - No Relevant Documents:**
```json
{
  "answer": "I couldn't find any relevant information in the uploaded documents to answer your question.",
  "content_blocks": [],
  "sources": [],
  "processing_time": 0.85
}
```

**Response (400 Bad Request):**
```json
{
  "error": "Question must be between 1 and 1000 characters",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Response (500 Internal Server Error):**
```json
{
  "error": "Failed to process query",
  "detail": null,
  "timestamp": "2024-01-15T10:30:00.123456Z"
}
```

**Status Codes:**
- `200` - Query processed successfully
- `400` - Invalid request parameters
- `500` - Server error during processing

## Data Models

### Document
```json
{
  "id": "string (UUID)",
  "filename": "string",
  "file_path": "string",
  "metadata": "DocumentMetadata",
  "status": "string (processing|completed|failed)",
  "error_message": "string|null"
}
```

### DocumentMetadata
```json
{
  "filename": "string",
  "file_size": "integer (bytes)",
  "content_type": "string (MIME type)",
  "upload_timestamp": "string (ISO 8601 datetime)",
  "chunk_count": "integer|null"
}
```

### Citation
```json
{
  "type": "string (char_location|page_location|content_block_location)",
  "cited_text": "string",
  "document_index": "integer",
  "document_title": "string",
  "start_char_index": "integer|null",
  "end_char_index": "integer|null",
  "start_page_number": "integer|null",
  "end_page_number": "integer|null",
  "start_block_index": "integer|null",
  "end_block_index": "integer|null"
}
```

### TextBlock
```json
{
  "type": "string (text)",
  "text": "string",
  "citations": "array of Citation|null"
}
```

### QueryRequest
```json
{
  "question": "string (required, 1-1000 chars)",
  "document_ids": "array of string (UUID)|null",
  "max_results": "integer (1-20, default: 5)",
  "include_citations": "boolean (default: true)"
}
```

### QueryResponse
```json
{
  "answer": "string",
  "content_blocks": "array of TextBlock",
  "sources": "array of Document",
  "processing_time": "number (seconds)"
}
```

### ErrorResponse
```json
{
  "error": "string",
  "detail": "string|null",
  "timestamp": "string (ISO 8601 datetime)"
}
```

### HealthResponse
```json
{
  "status": "string (healthy|unhealthy)",
  "timestamp": "string (ISO 8601 datetime)",
  "version": "string",
  "services": {
    "vector_database": "string (healthy|unhealthy)",
    "anthropic_api": "string (healthy|unhealthy)",
    "openai_api": "string (healthy|unhealthy)"
  }
}
```

## Error Handling

### HTTP Status Codes

- **200 OK** - Request successful
- **400 Bad Request** - Invalid request parameters or validation error
- **404 Not Found** - Resource not found
- **413 Payload Too Large** - File size exceeds limit
- **500 Internal Server Error** - Server error
- **503 Service Unavailable** - Service unhealthy

### Error Response Format

All error responses follow this format:
```json
{
  "error": "Human-readable error message",
  "detail": "Additional error details (optional)",
  "timestamp": "ISO 8601 datetime when error occurred"
}
```

### Common Error Scenarios

1. **File Upload Errors:**
   - File too large (>50MB)
   - Unsupported file format
   - Corrupted file

2. **Document Errors:**
   - Document not found
   - Document processing failed

3. **Query Errors:**
   - Empty question
   - Question too long (>1000 characters)
   - Invalid document IDs

4. **Service Errors:**
   - Vector database unavailable
   - Anthropic API errors
   - OpenAI API errors

## Rate Limits

Currently, no rate limits are enforced. In production, consider implementing:
- Request rate limiting per IP
- File upload size and frequency limits
- Query complexity limits

## Examples

### cURL Examples

#### Upload Document
```bash
curl -X POST "http://localhost:8000/api/v1/documents/upload" \
  -F "file=@research_paper.pdf" \
  -F "title=AI Research Paper"
```

#### List Documents
```bash
curl -X GET "http://localhost:8000/api/v1/documents" \
  -H "Accept: application/json"
```

#### Query Documents
```bash
curl -X POST "http://localhost:8000/api/v1/query" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "What are the main findings?",
    "include_citations": true,
    "max_results": 5
  }'
```

#### Delete Document
```bash
curl -X DELETE "http://localhost:8000/api/v1/documents/550e8400-e29b-41d4-a716-************"
```

### Python Examples

#### Upload Document
```python
import requests

url = "http://localhost:8000/api/v1/documents/upload"
files = {"file": open("research_paper.pdf", "rb")}
data = {"title": "AI Research Paper"}

response = requests.post(url, files=files, data=data)
print(response.json())
```

#### Query Documents
```python
import requests

url = "http://localhost:8000/api/v1/query"
payload = {
    "question": "What are the main contributions?",
    "include_citations": True,
    "max_results": 5
}

response = requests.post(url, json=payload)
print(response.json())
```

## Testing

### Health Check
Test API availability:
```bash
curl http://localhost:8000/api/v1/health
```

### Interactive Documentation
Access Swagger UI at: `http://localhost:8000/docs`
Access ReDoc at: `http://localhost:8000/redoc`

### Test Files
Use these sample files for testing:
- Small PDF (< 1MB)
- Text file with UTF-8 encoding
- Markdown file with headers

## Changelog

### Version 1.0.0
- Initial release
- Document upload and processing
- Vector similarity search
- Anthropic Citation API integration
- Basic error handling and validation
