# Installation Guide for Python 3.12.8 Compatibility

This guide helps resolve setuptools/pkg_resources compatibility issues with Python 3.12.8.

## 🔍 Problem Diagnosis

The error `AttributeError: module 'pkgutil' has no attribute 'ImpImporter'` occurs because:

1. **Python 3.12 removed `pkgutil.ImpImporter`** (deprecated `imp` module)
2. **Older setuptools versions** still reference this removed attribute
3. **Package dependency conflicts** during wheel building

## 🛠️ Solution Methods

### Method 1: Automated Installation (Recommended)

#### On macOS/Linux:
```bash
# Make the script executable
chmod +x install.sh

# Run the installation script
./install.sh
```

#### On Windows:
```cmd
# Run the installation script
install.bat
```

### Method 2: Manual Step-by-Step Installation

#### Step 1: Create Clean Environment
```bash
# Create new virtual environment
python -m venv venv_clean
source venv_clean/bin/activate  # macOS/Linux
# or
venv_clean\Scripts\activate     # Windows

# Verify Python version
python --version
```

#### Step 2: Upgrade Core Tools
```bash
# Upgrade pip and setuptools first
python -m pip install --upgrade pip
pip install --upgrade setuptools>=69.0.0 wheel>=0.42.0
```

#### Step 3: Install Dependencies in Stages
```bash
# Install core dependencies first
pip install -r requirements-core.txt

# Then install ML/AI dependencies
pip install -r requirements-ml.txt

# Finally install development tools
pip install pytest pytest-asyncio black isort
```

### Method 3: Alternative Package Versions

If you still encounter issues, try these alternative versions:

```bash
# Install with specific compatible versions
pip install setuptools==69.5.1 wheel==0.43.0
pip install fastapi==0.104.1 uvicorn==0.24.0
pip install anthropic==0.8.1 openai==1.6.1
pip install langchain==0.1.0 langchain-community==0.0.10
```

## 🚨 Troubleshooting Common Issues

### Issue 1: `pkgutil.ImpImporter` Error
**Solution:**
```bash
pip install --upgrade setuptools>=69.0.0
pip install --force-reinstall setuptools wheel
```

### Issue 2: `python-magic` Installation Fails
**Solution (macOS):**
```bash
# Install system dependency first
brew install libmagic

# Then install python package
pip install python-magic
```

**Solution (Ubuntu/Debian):**
```bash
sudo apt-get install libmagic1
pip install python-magic
```

**Alternative:** Remove `python-magic` from requirements if not needed.

### Issue 3: Numpy/Pandas Compatibility
**Solution:**
```bash
# Install with specific versions for Python 3.12
pip install numpy>=1.24.3,<2.0.0
pip install pandas>=2.1.4,<3.0.0
```

### Issue 4: LangChain Dependencies
**Solution:**
```bash
# Install LangChain components separately
pip install langchain-core
pip install langchain-community
pip install langchain-text-splitters
pip install langchain
```

## 🔧 Environment Verification

After installation, verify your environment:

```bash
# Check Python version
python --version

# Check key packages
python -c "import fastapi; print(f'FastAPI: {fastapi.__version__}')"
python -c "import anthropic; print(f'Anthropic: {anthropic.__version__}')"
python -c "import openai; print(f'OpenAI: {openai.__version__}')"
python -c "import langchain; print(f'LangChain: {langchain.__version__}')"

# Test import of main modules
python -c "from app.main import app; print('✅ App imports successfully')"
```

## 📋 Best Practices for Future

### 1. Use Version Ranges
Instead of pinning exact versions, use ranges:
```
fastapi>=0.104.1,<0.110.0
```

### 2. Separate Requirements Files
- `requirements-core.txt` - Essential dependencies
- `requirements-ml.txt` - ML/AI libraries
- `requirements-dev.txt` - Development tools

### 3. Regular Updates
```bash
# Check for outdated packages
pip list --outdated

# Update packages safely
pip install --upgrade package_name
```

### 4. Use pyproject.toml (Modern Approach)
Consider migrating to `pyproject.toml` for better dependency management:

```toml
[build-system]
requires = ["setuptools>=69.0.0", "wheel>=0.42.0"]

[project]
dependencies = [
    "fastapi>=0.104.1,<0.110.0",
    "uvicorn[standard]>=0.24.0,<0.30.0",
    # ... other dependencies
]
```

## 🆘 If All Else Fails

### Option 1: Use Python 3.11
```bash
# Install Python 3.11 with pyenv
pyenv install 3.11.7
pyenv local 3.11.7

# Create new environment
python -m venv venv_311
source venv_311/bin/activate
pip install -r requirements.txt
```

### Option 2: Use Docker
```bash
# Build with Docker (uses Python 3.11 base image)
docker build -t rag-citation-api .
docker run -p 8000:8000 rag-citation-api
```

### Option 3: Use Conda
```bash
# Create conda environment with Python 3.11
conda create -n rag-api python=3.11
conda activate rag-api
pip install -r requirements.txt
```

## ✅ Success Indicators

You'll know the installation is successful when:

1. ✅ No import errors when running `python -c "from app.main import app"`
2. ✅ Server starts with `python run.py`
3. ✅ API documentation loads at `http://localhost:8000/docs`
4. ✅ Health check returns 200 at `http://localhost:8000/api/v1/health`

## 📞 Getting Help

If you continue to experience issues:

1. **Check Python version compatibility**
2. **Review the full error traceback**
3. **Try the Docker approach as a fallback**
4. **Consider using Python 3.11 instead of 3.12**

The installation scripts and staged requirements files should resolve most Python 3.12.8 compatibility issues.
