#!/usr/bin/env python3
"""
Setup script for the RAG Citation API with Supabase integration.
"""
import asyncio
import os
import subprocess
import sys
from pathlib import Path

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

from app.config import settings
from app.services.storage_service import StorageService


async def install_dependencies():
    """Install Python dependencies."""
    print("Installing Python dependencies...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
        print("✓ Dependencies installed successfully")
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install dependencies: {e}")
        return False
    return True


async def setup_database():
    """Set up database with Alembic migrations."""
    print("Setting up database with Alembic...")

    # Check if DATABASE_URL is configured
    database_url = os.getenv('DATABASE_URL')
    if not database_url or 'PLACEHOLDER_PASSWORD' in database_url:
        print("⚠️  DATABASE_URL is not configured.")
        print("\nTo set up the database:")
        print("1. Go to your Supabase Dashboard > Settings > Database")
        print("2. Copy the 'Connection string' for direct connection")
        print("3. Add it to your .env file as DATABASE_URL=...")
        print("4. Run: python alembic_manager.py migrate")
        print("\nSkipping database setup for now...")
        return False

    try:
        # Test database connection first
        from app.database.connection import test_connection
        if not test_connection():
            print("✗ Database connection failed")
            return False

        print("✓ Database connection successful")

        # Check if we need to create initial migration
        alembic_versions_dir = Path("alembic/versions")
        if not alembic_versions_dir.exists() or not list(alembic_versions_dir.glob("*.py")):
            print("Creating initial migration...")
            result = subprocess.run([
                sys.executable, "alembic_manager.py", "generate", "Initial migration"
            ], capture_output=True, text=True)

            if result.returncode != 0:
                print(f"✗ Failed to create initial migration: {result.stderr}")
                return False

            print("✓ Initial migration created")

        # Apply migrations
        print("Applying migrations...")
        result = subprocess.run([
            sys.executable, "alembic_manager.py", "migrate"
        ], capture_output=True, text=True)

        if result.returncode != 0:
            print(f"✗ Failed to apply migrations: {result.stderr}")
            return False

        print("✓ Migrations applied successfully")
        return True

    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        return False


async def setup_storage():
    """Setup Supabase Storage bucket."""
    print("Setting up Supabase Storage...")
    try:
        storage_service = StorageService()
        success = await storage_service.create_bucket_if_not_exists()
        if success:
            print("✓ Storage bucket ready")
        else:
            print("✗ Failed to setup storage bucket")
            return False
    except Exception as e:
        print(f"✗ Storage setup failed: {e}")
        return False
    return True


def check_environment():
    """Check if required environment variables are set."""
    print("Checking environment configuration...")

    # Check required settings by trying to access them
    required_settings = [
        ("SUPABASE_URL", lambda: settings.supabase_url),
        ("SUPABASE_ANON_KEY", lambda: settings.supabase_anon_key),
        ("SUPABASE_SERVICE_ROLE_KEY", lambda: settings.supabase_service_role_key),
        ("ANTHROPIC_API_KEY", lambda: settings.anthropic_api_key),
        ("KDBAI_ENDPOINT", lambda: settings.kdbai_endpoint),
        ("KDBAI_API_KEY", lambda: settings.kdbai_api_key),
        ("OPENAI_API_KEY", lambda: settings.openai_api_key),
    ]

    missing_vars = []
    for var_name, getter in required_settings:
        try:
            value = getter()
            if not value or value.strip() == "":
                missing_vars.append(var_name)
        except Exception as e:
            missing_vars.append(f"{var_name} (Error: {str(e)})")

    if missing_vars:
        print("✗ Missing or invalid required environment variables:")
        for var in missing_vars:
            print(f"  - {var}")
        print("\nPlease set these variables in your .env file or environment.")
        print("See .env.example for reference.")
        print(f"\nCurrent .env file location: {Path('.env').absolute()}")
        return False

    print("✓ Environment configuration looks good")
    print(f"✓ Supabase URL: {settings.supabase_url}")
    return True


def create_upload_directory():
    """Create upload directory if it doesn't exist."""
    print("Creating upload directory...")
    try:
        settings.upload_dir.mkdir(parents=True, exist_ok=True)
        print(f"✓ Upload directory ready: {settings.upload_dir}")
    except Exception as e:
        print(f"✗ Failed to create upload directory: {e}")
        return False
    return True


async def main():
    """Main setup function."""
    print("🚀 Setting up RAG Citation API with Supabase integration")
    print("=" * 60)

    # Check environment
    if not check_environment():
        sys.exit(1)

    # Create upload directory
    if not create_upload_directory():
        sys.exit(1)

    # Install dependencies
    if not await install_dependencies():
        sys.exit(1)

    # Set up database (continue even if this fails)
    database_success = await setup_database()
    if not database_success:
        print("⚠️  Continuing setup without database setup...")
        print("   You'll need to configure DATABASE_URL and run migrations manually.")

    # Setup storage
    if not await setup_storage():
        sys.exit(1)

    print("\n" + "=" * 60)
    print("✅ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Start the API server: python run.py")
    print("2. Visit http://localhost:8000/docs for API documentation")
    print("3. Test document upload with authentication")


if __name__ == "__main__":
    asyncio.run(main())
