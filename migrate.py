#!/usr/bin/env python3
"""
Database migration runner for the RAG Citation API.
Manages database schema changes using SQL migration files.
"""
import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import List, Tuple

import asyncpg

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

from app.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

MIGRATIONS_DIR = Path("migrations")


class MigrationRunner:
    """Handles database migrations."""

    def __init__(self):
        self.connection = None

    async def connect(self):
        """Connect to the database."""
        try:
            # For Supabase, we need to use the database URL from the project settings
            # The format should be: postgresql://postgres:[password]@db.[project-id].supabase.co:5432/postgres

            # Extract project ID from Supabase URL
            # Format: https://project-id.supabase.co -> project-id
            project_id = settings.supabase_url.replace('https://', '').replace('http://', '').split('.')[0]

            # Construct the database connection URL
            # Note: You need to get the database password from Supabase dashboard > Settings > Database
            # For now, we'll try to connect using the service role key as password
            db_host = f"db.{project_id}.supabase.co"

            # Try different connection approaches
            connection_attempts = [
                # Attempt 1: Using service role key as password (might work for some setups)
                f"postgresql://postgres:{settings.supabase_service_role_key}@{db_host}:5432/postgres",
                # Attempt 2: Using the project reference directly
                f"postgresql://postgres.{project_id}:5432/postgres",
            ]

            last_error = None
            for db_url in connection_attempts:
                try:
                    logger.info(f"Attempting to connect to database...")
                    self.connection = await asyncpg.connect(db_url)
                    logger.info("Connected to database successfully")
                    return
                except Exception as e:
                    last_error = e
                    logger.warning(f"Connection attempt failed: {str(e)}")
                    continue

            # If all attempts failed, raise the last error
            raise last_error

        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            logger.error(f"Supabase URL: {settings.supabase_url}")
            logger.error("\nTo fix this issue:")
            logger.error("1. Go to your Supabase dashboard > Settings > Database")
            logger.error("2. Copy the 'Connection string' for direct database access")
            logger.error("3. Add DATABASE_URL to your .env file with the connection string")
            logger.error("4. Or use the Supabase client for migrations instead of direct PostgreSQL")
            raise

    async def disconnect(self):
        """Disconnect from the database."""
        if self.connection:
            await self.connection.close()
            logger.info("Disconnected from database")

    async def create_migrations_table(self):
        """Create the migrations tracking table if it doesn't exist."""
        await self.connection.execute("""
            CREATE TABLE IF NOT EXISTS migrations (
                id SERIAL PRIMARY KEY,
                filename TEXT NOT NULL UNIQUE,
                applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            )
        """)
        logger.info("Migrations table ready")

    async def get_applied_migrations(self) -> List[str]:
        """Get list of applied migration filenames."""
        rows = await self.connection.fetch(
            "SELECT filename FROM migrations ORDER BY id"
        )
        return [row['filename'] for row in rows]

    def get_migration_files(self) -> List[Tuple[str, Path]]:
        """Get list of migration files sorted by filename."""
        if not MIGRATIONS_DIR.exists():
            logger.warning(f"Migrations directory {MIGRATIONS_DIR} does not exist")
            return []

        files = []
        for file_path in sorted(MIGRATIONS_DIR.glob("*.sql")):
            files.append((file_path.name, file_path))

        return files

    async def apply_migration(self, filename: str, file_path: Path):
        """Apply a single migration file."""
        logger.info(f"Applying migration: {filename}")

        # Read migration file
        with open(file_path, 'r') as f:
            sql_content = f.read()

        # Execute migration in a transaction
        async with self.connection.transaction():
            await self.connection.execute(sql_content)
            await self.connection.execute(
                "INSERT INTO migrations (filename) VALUES ($1)",
                filename
            )

        logger.info(f"Successfully applied migration: {filename}")

    async def run_migrations(self):
        """Run all pending migrations."""
        await self.create_migrations_table()

        applied_migrations = await self.get_applied_migrations()
        migration_files = self.get_migration_files()

        pending_migrations = [
            (filename, file_path) for filename, file_path in migration_files
            if filename not in applied_migrations
        ]

        if not pending_migrations:
            logger.info("No pending migrations")
            return

        logger.info(f"Found {len(pending_migrations)} pending migrations")

        for filename, file_path in pending_migrations:
            await self.apply_migration(filename, file_path)

        logger.info("All migrations completed successfully")

    async def show_status(self):
        """Show migration status."""
        await self.create_migrations_table()

        applied_migrations = await self.get_applied_migrations()
        migration_files = self.get_migration_files()

        print("\nMigration Status:")
        print("=" * 50)

        for filename, _ in migration_files:
            status = "✓ Applied" if filename in applied_migrations else "✗ Pending"
            print(f"{filename:<30} {status}")

        print(f"\nTotal migrations: {len(migration_files)}")
        print(f"Applied: {len(applied_migrations)}")
        print(f"Pending: {len(migration_files) - len(applied_migrations)}")


async def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(description="Database migration runner")
    parser.add_argument(
        "--status",
        action="store_true",
        help="Show migration status"
    )

    args = parser.parse_args()

    runner = MigrationRunner()

    try:
        await runner.connect()

        if args.status:
            await runner.show_status()
        else:
            await runner.run_migrations()

    except Exception as e:
        logger.error(f"Migration failed: {e}")
        sys.exit(1)

    finally:
        await runner.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
